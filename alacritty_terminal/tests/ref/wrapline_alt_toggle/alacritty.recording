[1m[7m%[27m[1m[0m                                                                                                                                          
 

[0m[27m[24m[J[38;5;0;48;5;9m UL [0m[38;5;7;48;5;8m /…/tests/ref/wrapline_alt_t… [0m[38;5;0;48;5;9m fix_newline_flag [0m [K

[0m[27m[24m[J[38;5;0;48;5;9m UL [0m[38;5;7;48;5;8m /…/tests/ref/wrapline_alt_t… [0m[38;5;0;48;5;9m fix_newline_flag [0m [K[?2004hf[37mor i in {1..$(tput cols)}; do echo -ne "a"; done && echo -ne "\e[?1049h" && echo -ne [37m"[37mx" && echo -ne "\e[?1049l" && echo -ne "a"[39m[K[A[11Cf[39mo[39mr[39m [39mi[39m [39mi[39mn[39m [39m{[39m1[39m.[39m.[39m$[39m([39mt[39mp[39mu[39mt[39m [39mc[39mo[39ml[39ms[39m)[39m}[39m;[39m [39md[39mo[39m [39me[39mc[39mh[39mo[39m [39m-[39mn[39me[39m [39m"[39ma[39m"[39m;[39m [39md[39mo[39mn[39me[39m [39m&[39m&[39m [39me[39mc[39mh[39mo[39m [39m-[39mn[39me[39m [39m"[39m\[39me[39m[[39m?[39m1[39m0[39m4[39m9[39mh[39m"[39m [39m&[39m&[39m [39me[39mc[39mh[39mo[39m [39m-[39mn[39me[39m "[39mx[39m"[39m [39m&[39m&[39m [39me[39mc[39mh[39mo[39m [39m-[39mn[39me[39m [39m"[39m\[39me[39m[[39m?[39m1[39m0[39m4[39m9[39ml[39m"[39m [39m&[39m&[39m [39me[39mc[39mh[39mo[39m [39m-[39mn[39me[39m [39m"[39ma[39m"[?2004l

aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa[?1049hx[?1049la[1m[7m%[27m[1m[0m                                                                                                                                          
 

[0m[27m[24m[J[38;5;0;48;5;9m UL [0m[38;5;7;48;5;8m /…/tests/ref/wrapline_alt_t… [0m[38;5;0;48;5;9m fix_newline_flag [0m [K

[0m[27m[24m[J[38;5;0;48;5;9m UL [0m[38;5;7;48;5;8m /…/tests/ref/wrapline_alt_t… [0m[38;5;0;48;5;9m fix_newline_flag [0m [K[?2004he[37mxit[39me[39mx[39mi[39mt[?2004l

