[1m[7m%[27m[1m[0m                                                                                                                                                                                                                                                                                       
 

[0m[27m[24m[J[0;30;101m UL [0m[0;37;100m ~/…/tests/ref/grid_reset [0m[0;30;101m dynamic-alloc [0m [K[?2004hf[90mor i in {0..100}; do echo $i; done[39m[34Df[39mo[39mr[39m [39mi[39m [39mi[39mn[39m [39m{[39m0[39m.[39m.[39m1[39m0[39m0[39m}[39m;[39m [39md[39mo[39m [39me[39mc[39mh[39mo[39m [39m$[39mi[39m;[39m [39md[39mo[39mn[39me[?2004l

0
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
[1m[7m%[27m[1m[0m                                                                                                                                                                                                                                                                                       
 

[0m[27m[24m[J[0;30;101m UL [0m[0;37;100m ~/…/tests/ref/grid_reset [0m[0;30;101m dynamic-alloc [0m [K[?2004hr[90mm *[39mr[39me[90ms[90me[90mt[39m[39ms[39me[39mt[?2004l

c]104[!p[?3;4l[4l>
[1m[7m%[27m[1m[0m                                                                                                                                                                                                                                                                                       
 

[0m[27m[24m[J[0;30;101m UL [0m[0;37;100m ~/…/tests/ref/grid_reset [0m[0;30;101m dynamic-alloc [0m [K[?2004h