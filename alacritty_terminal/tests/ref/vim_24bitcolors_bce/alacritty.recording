[1m[7m%[27m[1m[0m                                                                                                                       
 

[0m[27m[24m[Jjwi<PERSON>@kurast.local [01;32m➜ [01;32m [36m~/code/alacritty [00m [K[?1h=[?2004h

bck-i-search: _[K[A[25Cm[4mv[24m ../../../{grid.json,size.json,alacritty.recording} ./[1B[82Dv_[A[25Cc[24md [4mv[4mi[24mm_large_window_scroll                              [1B[81Di_[A[26C[1C[4mi[4mm[24m[1B[30Dm_[A[25C   [24m [24m [24m                     [1B
[K[A[40C[1m[31mv[0m[39m[0m[35mv[35mi[39m[35mv[35mi[35mm[39m [36ms[39m[36ms[36mr[39m[36mr[36mc[39m[36mc[36m/[39m[36m/[36mr[39m[36mr[36me[39m[36me[36mn[39m[36mn[36md[39m[36md[36merer/[0m[39m[36mr[39m[39m [?1l>[?2004l[1B
[?1049h[?1h=[2;1H▽[6n[2;1H  [1;1H[1;57r[?12;25h[?12l[?25h[27m[23m[m[38;2;234;234;234m[48;2;0;0;0m[H[2J[?25l[57;1H"src/renderer" is a directory[>c[1;1H[38;2;118;120;118m" ============================================================================[m[38;2;234;234;234m[48;2;0;0;0m  [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;118;120;118m" Netrw Directory Listing                                        [m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225m(netrw v156)[m[38;2;234;234;234m[48;2;0;0;0m   [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;118;120;118m"   /Users/<USER>/code/alacritty/src/renderer[m[38;2;234;234;234m[48;2;0;0;0m[36C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;118;120;118m"   Sorted by[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224m      name[m[38;2;234;234;234m[48;2;0;0;0m[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;118;120;118m"   Sort sequence:[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224m [\/]$[m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m,[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224m\<core\%(\.\d\+\)\=\>[m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m,[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224m\.h$[m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m,[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224m\.c$[m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m,[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224m\.cpp$[m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m,[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224m\~\=\*$[m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m,[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224m*[m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m,[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224m\.o$[m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m,[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224m\[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224m[48;2;42;42;42m.[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mobj$[m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m,[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224m\.info$[m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m,[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224m\.swp$[m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m,[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224m\.bak$[m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m,[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224m\~$[m[38;2;234;234;234m[48;2;0;0;0m
[38;2;118;120;118m"   Quick Help: [m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225m<F1>[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m:[m[38;2;234;234;234m[48;2;0;0;0mhelp  [38;2;140;182;225m-[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m:[m[38;2;234;234;234m[48;2;0;0;0mgo up dir  [38;2;140;182;225mD[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m:[m[38;2;234;234;234m[48;2;0;0;0mdelete  [38;2;140;182;225mR[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m:[m[38;2;234;234;234m[48;2;0;0;0mrename  [38;2;140;182;225ms[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m:[m[38;2;234;234;234m[48;2;0;0;0msort-by  [38;2;140;182;225mx[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m:[m[38;2;234;234;234m[48;2;0;0;0mspecial[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;118;120;118m" ==============================================================================[m[38;2;234;234;234m[48;2;0;0;0m[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;140;182;225m[48;2;42;42;42m../[m[38;2;234;234;234m[48;2;0;0;0m[48;2;42;42;42m                                                                                                                     [m[38;2;234;234;234m[48;2;0;0;0m[9;1H[38;2;140;182;225m./[m[38;2;234;234;234m[48;2;0;0;0m[78C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
mod.rs[74C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m~                                                                                                                       [12;1H~                                                                                                                       [13;1H~                                                                                                                       [14;1H~                                                                                                                       [15;1H~                                                                                                                       [16;1H~                                                                                                                       [17;1H~                                                                                                                       [18;1H~                                                                                                                       [19;1H~                                                                                                                       [20;1H~                                                                                                                       [21;1H~                                                                                                                       [22;1H~                                                                                                                       [23;1H~                                                                                                                       [24;1H~                                                                                                                       [25;1H~                                                                                                                       [26;1H~                                                                                                                       [27;1H~                                                                                                                       [28;1H~                                                                                                                       [29;1H~                                                                                                                       [30;1H~                                                                                                                       [31;1H~                                                                                                                       [32;1H~                                                                                                                       [33;1H~                                                                                                                       [34;1H~                                                                                                                       [35;1H~                                                                                                                       [36;1H~                                                                                                                       [37;1H~                                                                                                                       [38;1H~                                                                                                                       [39;1H~                                                                                                                       [40;1H~                                                                                                                       [41;1H~                                                                                                                       [42;1H~                                                                                                                       [43;1H~                                                                                                                       [44;1H~                                                                                                                       [45;1H~                                                                                                                       [46;1H~                                                                                                                       [47;1H~                                                                                                                       [48;1H~                                                                                                                       [49;1H~                                                                                                                       [50;1H~                                                                                                                       [51;1H~                                                                                                                       [52;1H~                                                                                                                       [53;1H~                                                                                                                       [54;1H~                                                                                                                       [55;1H~                                                                                                                       [56;1H~                                                                                                                       [m[38;2;234;234;234m[48;2;0;0;0m[57;103H8,1[11CAll[8;1H[?12l[?25h[?25l[38;2;140;182;225m../[m[38;2;234;234;234m[48;2;0;0;0m                                                                             [8;82H[K[9;1H[38;2;140;182;225m[48;2;42;42;42m./[m[38;2;234;234;234m[48;2;0;0;0m[48;2;42;42;42m                                                                                                                      [m[38;2;234;234;234m[48;2;0;0;0m[57;103H9[9;1H[?12l[?25h[?25l[38;2;140;182;225m./[m[38;2;234;234;234m[48;2;0;0;0m                                                                              [9;82H[K[10;1H[48;2;42;42;42mmod.rs                                                                                                                  [m[38;2;234;234;234m[48;2;0;0;0m[57;103H10,1[10;1H[?12l[?25h[?25l[57;2H~/code/alacritty/src/renderer/mod.rs"[57;103H[K[57;40H1354L, 40527C[1;1H[38;2;66;66;66m 864 [m[38;2;234;234;234m[48;2;0;0;0m    }                                                                       [24C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 865 [m[38;2;234;234;234m[48;2;0;0;0m                                                                            [24C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 866 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mdeactivate[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m) {         [36C [24C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 867 [m[38;2;234;234;234m[48;2;0;0;0m        [38;2;207;171;224munsafe[m[38;2;234;234;234m[48;2;0;0;0m {  [57C [24C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 868 [m[38;2;234;234;234m[48;2;0;0;0m            [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mUseProgram[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m);                                                                      [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[5;107H[K[6;1H[38;2;66;66;66m 869 [m[38;2;234;234;234m[48;2;0;0;0m        }                                                                   [24C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 870 [m[38;2;234;234;234m[48;2;0;0;0m    }                                                                       [24C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 871 [m[38;2;234;234;234m[48;2;0;0;0m[75C [24C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 872 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mnew[m[38;2;234;234;234m[48;2;0;0;0m([60C [24C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 873 [m[38;2;234;234;234m[48;2;0;0;0m        config: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mConfig,                                                                            [10;107H[K[11;1H[38;2;66;66;66m 874[m[38;2;234;234;234m[48;2;0;0;0m[1C        size: Size[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mPixels[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mu32[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>>[m[38;2;234;234;234m[48;2;0;0;0m                                                                     [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[11;107H[K[12;1H[38;2;66;66;66m 875[m[38;2;234;234;234m[48;2;0;0;0m[1C    ) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mResult[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mShaderProgram, ShaderCreationError[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m {                                               [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[12;107H[K[13;1H[38;2;66;66;66m 876[m[38;2;234;234;234m[48;2;0;0;0m[1C        [38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m vertex_source [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m [38;2;223;101;102mcfg![m[38;2;234;234;234m[48;2;0;0;0m(feature [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;197;209;92m"live-shader-reload"[m[38;2;234;234;234m[48;2;0;0;0m) {                               [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[13;107H[K[14;1H[38;2;66;66;66m 877[m[38;2;234;234;234m[48;2;0;0;0m[1C            [38;2;237;158;86mNone[m[38;2;234;234;234m[48;2;0;0;0m                                                                                    [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[14;107H[K[15;1H[38;2;66;66;66m 878[m[38;2;234;234;234m[48;2;0;0;0m[1C        } [38;2;207;171;224melse[m[38;2;234;234;234m[48;2;0;0;0m {                                                                                    [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[15;107H[K[16;1H[38;2;66;66;66m 879[m[38;2;234;234;234m[48;2;0;0;0m[1C            [38;2;237;158;86mSome[m[38;2;234;234;234m[48;2;0;0;0m(TEXT_SHADER_V)                                                                     [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[16;107H[K[17;1H[38;2;66;66;66m 880[m[38;2;234;234;234m[48;2;0;0;0m[1C        };                                                                                          [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[17;107H[K[18;1H[38;2;66;66;66m 881[m[38;2;234;234;234m[48;2;0;0;0m[1C        [38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m vertex_shader [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mShaderProgram::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mcreate_shader[m[38;2;234;234;234m[48;2;0;0;0m(                                           [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[18;107H[K[19;1H[38;2;66;66;66m 882[m[38;2;234;234;234m[48;2;0;0;0m[1C            TEXT_SHADER_V_PATH,                                                                     [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[19;107H[K[20;1H[38;2;66;66;66m 883[m[38;2;234;234;234m[48;2;0;0;0m[1C            [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mVERTEX_SHADER,                                                                      [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[20;107H[K[21;1H[38;2;66;66;66m 884[m[38;2;234;234;234m[48;2;0;0;0m[1C            vertex_source                                                                           [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[21;107H[K[22;1H[38;2;66;66;66m 885[m[38;2;234;234;234m[48;2;0;0;0m[1C        )[38;2;129;202;191m?[m[38;2;234;234;234m[48;2;0;0;0m;                                                                                         [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[22;107H[K[23;1H[38;2;66;66;66m 886[m[38;2;234;234;234m[48;2;0;0;0m[1C        [38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m frag_source [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m [38;2;223;101;102mcfg![m[38;2;234;234;234m[48;2;0;0;0m(feature [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;197;209;92m"live-shader-reload"[m[38;2;234;234;234m[48;2;0;0;0m) {                                 [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[23;107H[K[24;1H[38;2;66;66;66m 887[m[38;2;234;234;234m[48;2;0;0;0m[1C            [38;2;237;158;86mNone[m[38;2;234;234;234m[48;2;0;0;0m                                                                                    [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[24;107H[K[25;1H[38;2;66;66;66m 888[m[38;2;234;234;234m[48;2;0;0;0m[1C        } [38;2;207;171;224melse[m[38;2;234;234;234m[48;2;0;0;0m {                                                                                    [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[25;107H[K[26;1H[38;2;66;66;66m 889[m[38;2;234;234;234m[48;2;0;0;0m[1C            [38;2;237;158;86mSome[m[38;2;234;234;234m[48;2;0;0;0m(TEXT_SHADER_F)                                                                     [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[26;107H[K[27;1H[38;2;66;66;66m 890[m[38;2;234;234;234m[48;2;0;0;0m[1C        };                                                                                          [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[27;107H[K[28;1H[38;2;66;66;66m 891[m[38;2;234;234;234m[48;2;0;0;0m[1C        [38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m fragment_shader [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mShaderProgram::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mcreate_shader[m[38;2;234;234;234m[48;2;0;0;0m(                                         [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[28;107H[K[29;1H[38;2;66;66;66m 892[m[38;2;234;234;234m[48;2;0;0;0m[1C            TEXT_SHADER_F_PATH,                                                                     [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[29;107H[K[30;1H[38;2;66;66;66m 893[m[38;2;234;234;234m[48;2;0;0;0m[1C            [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mFRAGMENT_SHADER,                                                                    [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[30;107H[K[31;1H[38;2;66;66;66m 894[m[38;2;234;234;234m[48;2;0;0;0m[1C            frag_source                                                                             [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[31;107H[K[32;1H[38;2;66;66;66m 895[m[38;2;234;234;234m[48;2;0;0;0m[1C        )[38;2;129;202;191m?[m[38;2;234;234;234m[48;2;0;0;0m;                                                                                         [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[32;107H[K[33;1H[38;2;66;66;66m 896[m[38;2;234;234;234m[48;2;0;0;0m[1C        [38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m program [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mShaderProgram::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mcreate_program[m[38;2;234;234;234m[48;2;0;0;0m(vertex_shader, fragment_shader);                [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[33;107H[K[34;1H[38;2;66;66;66m 897[m[38;2;234;234;234m[48;2;0;0;0m[1C                                                                                                    [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[34;107H[K[35;1H[38;2;66;66;66m 898[m[38;2;234;234;234m[48;2;0;0;0m[1C        [38;2;207;171;224munsafe[m[38;2;234;234;234m[48;2;0;0;0m {                                                                                    [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[35;107H[K[36;1H[38;2;66;66;66m 899[m[38;2;234;234;234m[48;2;0;0;0m[1C            [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mDeleteShader[m[38;2;234;234;234m[48;2;0;0;0m(vertex_shader);                                                        [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[36;107H[K[37;1H[38;2;66;66;66m 900[m[38;2;234;234;234m[48;2;0;0;0m[1C            [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mDeleteShader[m[38;2;234;234;234m[48;2;0;0;0m(fragment_shader);                                                      [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[37;107H[K[38;1H[38;2;66;66;66m 901[m[38;2;234;234;234m[48;2;0;0;0m[1C            [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mUseProgram[m[38;2;234;234;234m[48;2;0;0;0m(program);                                                                [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[38;107H[K[39;1H[38;2;66;66;66m 902[m[38;2;234;234;234m[48;2;0;0;0m[1C        }                                                                                           [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[39;107H[K[40;1H[38;2;66;66;66m 903[m[38;2;234;234;234m[48;2;0;0;0m[1C                                                                                                    [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[40;107H[K[41;1H[38;2;66;66;66m 904[m[38;2;234;234;234m[48;2;0;0;0m[1C        [38;2;223;101;102mmacro_rules![m[38;2;234;234;234m[48;2;0;0;0m cptr {                                                                         [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[41;107H[K[42;1H[38;2;66;66;66m 905[m[38;2;234;234;234m[48;2;0;0;0m[1C            ([38;2;237;158;86m$thing[m[38;2;234;234;234m[48;2;0;0;0m:expr) [38;2;129;202;191m=>[m[38;2;234;234;234m[48;2;0;0;0m { [38;2;237;158;86m$thing[m[38;2;234;234;234m[48;2;0;0;0m.[38;2;140;182;225mas_ptr[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mconst[m[38;2;234;234;234m[48;2;0;0;0m _ }                                        [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[42;107H[K[43;1H[38;2;66;66;66m 906[m[38;2;234;234;234m[48;2;0;0;0m[1C        }                                                                                           [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[43;107H[K[44;1H[38;2;66;66;66m 907[m[38;2;234;234;234m[48;2;0;0;0m[1C                                                                                                    [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[44;107H[K[45;1H[38;2;66;66;66m 908[m[38;2;234;234;234m[48;2;0;0;0m[1C        [38;2;223;101;102mmacro_rules![m[38;2;234;234;234m[48;2;0;0;0m assert_uniform_valid {                                                         [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[45;107H[K[46;1H[38;2;66;66;66m 909[m[38;2;234;234;234m[48;2;0;0;0m[1C            ([38;2;237;158;86m$uniform[m[38;2;234;234;234m[48;2;0;0;0m:expr) [38;2;129;202;191m=>[m[38;2;234;234;234m[48;2;0;0;0m {                                                                    [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[46;107H[K[47;1H[38;2;66;66;66m 910[m[38;2;234;234;234m[48;2;0;0;0m[1C                [38;2;223;101;102massert![m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m$uniform[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m!=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mINVALID_VALUE [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mi32[m[38;2;234;234;234m[48;2;0;0;0m);                                      [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[47;107H[K[48;1H[38;2;66;66;66m 911[m[38;2;234;234;234m[48;2;0;0;0m[1C                [38;2;223;101;102massert![m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m$uniform[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m!=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mINVALID_OPERATION [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mi32[m[38;2;234;234;234m[48;2;0;0;0m);                                  [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[48;107H[K[49;1H[38;2;66;66;66m 912[m[38;2;234;234;234m[48;2;0;0;0m[1C            };                                                                                      [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[49;107H[K[50;1H[38;2;66;66;66m 913[m[38;2;234;234;234m[48;2;0;0;0m[1C            ( [38;2;223;101;102m$([m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m$uniform[m[38;2;234;234;234m[48;2;0;0;0m:expr [38;2;223;101;102m),*[m[38;2;234;234;234m[48;2;0;0;0m ) [38;2;129;202;191m=>[m[38;2;234;234;234m[48;2;0;0;0m {                                                           [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[50;107H[K[51;1H[38;2;66;66;66m 914[m[38;2;234;234;234m[48;2;0;0;0m[1C                [38;2;223;101;102m$([m[38;2;234;234;234m[48;2;0;0;0m [38;2;223;101;102massert_uniform_valid![m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m$uniform[m[38;2;234;234;234m[48;2;0;0;0m); [38;2;223;101;102m)*[m[38;2;234;234;234m[48;2;0;0;0m                                              [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[51;107H[K[52;1H[38;2;66;66;66m 915[m[38;2;234;234;234m[48;2;0;0;0m[1C            };                                                                                      [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[52;107H[K[53;1H[38;2;66;66;66m 916[m[38;2;234;234;234m[48;2;0;0;0m[1C        }                                                                                           [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[53;107H[K[54;1H[38;2;66;66;66m 917[m[38;2;234;234;234m[48;2;0;0;0m[1C                                                                                                    [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[54;107H[K[55;1H[38;2;66;66;66m 918[m[38;2;234;234;234m[48;2;0;0;0m[1C        [38;2;118;120;118m// get uniform locations[m[38;2;234;234;234m[48;2;0;0;0m                                                                    [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[55;107H[K[56;1H[38;2;66;66;66m 919[m[38;2;234;234;234m[48;2;0;0;0m[1C        [38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m (projection, term_dim, cell_dim, visual_bell, background) [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224munsafe[m[38;2;234;234;234m[48;2;0;0;0m {                    [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[56;107H[K[57;103H891,1[9C66%[28;6H[?12l[?25h[?25l[27m[23m[m[38;2;234;234;234m[48;2;0;0;0m[H[2J[1;1H[38;2;66;66;66m   1 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// Copyright 2016 Joe Wilm, The Alacritty Project Contributors[m[38;2;234;234;234m[48;2;0;0;0m[38C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m   2 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m//[m[38;2;234;234;234m[48;2;0;0;0m[98C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m   3 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// Licensed under the Apache License, Version 2.0 (the "License");[m[38;2;234;234;234m[48;2;0;0;0m[34C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m   4 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// you may not use this file except in compliance with the License.[m[38;2;234;234;234m[48;2;0;0;0m[33C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m   5 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// You may obtain a copy of the License at[m[38;2;234;234;234m[48;2;0;0;0m[58C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m   6 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m//[m[38;2;234;234;234m[48;2;0;0;0m[98C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m   7 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m//     http://www.apache.org/licenses/LICENSE-2.0[m[38;2;234;234;234m[48;2;0;0;0m[51C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m   8 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m//[m[38;2;234;234;234m[48;2;0;0;0m[98C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m   9 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// Unless required by applicable law or agreed to in writing, software[m[38;2;234;234;234m[48;2;0;0;0m[30C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  10 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// distributed under the License is distributed on an "AS IS" BASIS,[m[38;2;234;234;234m[48;2;0;0;0m[32C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  11 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.[m[38;2;234;234;234m[48;2;0;0;0m[25C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  12 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// See the License for the specific language governing permissions and[m[38;2;234;234;234m[48;2;0;0;0m[30C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  13 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// limitations under the License.[m[38;2;234;234;234m[48;2;0;0;0m[67C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  14 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mstd::collections::[m[38;2;234;234;234m[48;2;0;0;0mHashMap;[70C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  15 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mstd::hash::[m[38;2;234;234;234m[48;2;0;0;0mBuildHasherDefault;[66C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  16 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mstd::fs::[m[38;2;234;234;234m[48;2;0;0;0mFile;[82C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  17 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mstd::io::[m[38;2;234;234;234m[48;2;0;0;0m{[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, Read};[74C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  18 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mstd::mem::[m[38;2;234;234;234m[48;2;0;0;0msize_of;[78C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  19 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mstd::path::[m[38;2;234;234;234m[48;2;0;0;0m{PathBuf};[75C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  20 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mstd::[m[38;2;234;234;234m[48;2;0;0;0mptr;[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  21 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mstd::sync::[m[38;2;234;234;234m[48;2;0;0;0mmpsc;[80C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  22 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  23 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m cgmath;[89C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  24 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mfnv::[m[38;2;234;234;234m[48;2;0;0;0mFnvHasher;[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  25 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mfont::[m[38;2;234;234;234m[48;2;0;0;0m{[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, Rasterizer, Rasterize, RasterizedGlyph, FontDesc, GlyphKey, FontKey};[14C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  26 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mgl::types::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m;[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  27 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m gl;[93C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  28 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mindex::[m[38;2;234;234;234m[48;2;0;0;0m{Line, Column, RangeInclusive};[58C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  29 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mnotify::[m[38;2;234;234;234m[48;2;0;0;0m{Watcher [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m WatcherApi, RecommendedWatcher [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m Watcher, op};[29C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  30 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  31 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mconfig::[m[38;2;234;234;234m[48;2;0;0;0m{[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, Config, Delta};[66C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  32 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mterm::[m[38;2;234;234;234m[48;2;0;0;0m{[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, cell, RenderableCell};[61C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  33 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mwindow::[m[38;2;234;234;234m[48;2;0;0;0m{Size, Pixels};[73C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  34 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  35 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m Rgb;[92C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  36 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  37 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// Shader paths for live reload[m[38;2;234;234;234m[48;2;0;0;0m[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  38 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mstatic[m[38;2;234;234;234m[48;2;0;0;0m TEXT_SHADER_F_PATH: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'static[23m[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstr[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;223;101;102mconcat![m[38;2;234;234;234m[48;2;0;0;0m([38;2;223;101;102menv![m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"CARGO_MANIFEST_DIR"[m[38;2;234;234;234m[48;2;0;0;0m), [38;2;197;209;92m"/res/text.f.glsl"[m[38;2;234;234;234m[48;2;0;0;0m);  [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  39 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mstatic[m[38;2;234;234;234m[48;2;0;0;0m TEXT_SHADER_V_PATH: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'static[23m[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstr[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;223;101;102mconcat![m[38;2;234;234;234m[48;2;0;0;0m([38;2;223;101;102menv![m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"CARGO_MANIFEST_DIR"[m[38;2;234;234;234m[48;2;0;0;0m), [38;2;197;209;92m"/res/text.v.glsl"[m[38;2;234;234;234m[48;2;0;0;0m);  [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  40 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  41 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// Shader source which is used when live-shader-reload feature is disable[m[38;2;234;234;234m[48;2;0;0;0m[27C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  42 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mstatic[m[38;2;234;234;234m[48;2;0;0;0m TEXT_SHADER_F: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'static[23m[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstr[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;223;101;102minclude_str![m[38;2;234;234;234m[48;2;0;0;0m([50C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  43 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;223;101;102mconcat![m[38;2;234;234;234m[48;2;0;0;0m([38;2;223;101;102menv![m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"CARGO_MANIFEST_DIR"[m[38;2;234;234;234m[48;2;0;0;0m), [38;2;197;209;92m"/res/text.f.glsl"[m[38;2;234;234;234m[48;2;0;0;0m)[41C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  44 [m[38;2;234;234;234m[48;2;0;0;0m);[98C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  45 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mstatic[m[38;2;234;234;234m[48;2;0;0;0m TEXT_SHADER_V: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'static[23m[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstr[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;223;101;102minclude_str![m[38;2;234;234;234m[48;2;0;0;0m([50C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  46 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;223;101;102mconcat![m[38;2;234;234;234m[48;2;0;0;0m([38;2;223;101;102menv![m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"CARGO_MANIFEST_DIR"[m[38;2;234;234;234m[48;2;0;0;0m), [38;2;197;209;92m"/res/text.v.glsl"[m[38;2;234;234;234m[48;2;0;0;0m)[41C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  47 [m[38;2;234;234;234m[48;2;0;0;0m);[98C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  48 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  49 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m/// `LoadGlyph` allows for copying a rasterized glyph into graphics memory[m[38;2;234;234;234m[48;2;0;0;0m[26C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  50 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mtrait[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mLoadGlyph[m[38;2;234;234;234m[48;2;0;0;0m {[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  51 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// Load the rasterized glyph into GPU memory[m[38;2;234;234;234m[48;2;0;0;0m[51C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  52 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mload_glyph[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, rasterized: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mRasterizedGlyph) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m Glyph;[32C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  53 [m[38;2;234;234;234m[48;2;0;0;0m}[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  54 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  55 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224menum[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mMsg[m[38;2;234;234;234m[48;2;0;0;0m {[90C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  56 [m[38;2;234;234;234m[48;2;0;0;0m    ShaderReload,[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H1,1[11CTop[1;6H[?12l[?25h[?25l[57;103H[K[57;1H:[?12l[?25hs[?25l[?12l[?25he[?25l[?12l[?25ht[?25l[?12l[?25h[?25l ft=yaml[?12l[?25h[?25l[57;12H[K[57;12H[?12l[?25h[?25l[57;11H[K[57;11H[?12l[?25h[?25l[57;10H[K[57;10H[?12l[?25h[?25l[57;9H[K[57;9H[?12l[?25h[?25l[57;8H[K[57;8H[?12l[?25h[?25l[57;7H[K[57;7H[?12l[?25h[?25l[57;6H[K[57;6H[?12l[?25h[?25l[?12l[?25h[?25l[57;4H[K[57;4H[?12l[?25h[?25l[57;3H[K[57;3H[?12l[?25hc[?25l[?12l[?25h[?25l[57;3H[K[57;3H[?12l[?25h[?25l[57;2H[K[57;2H[?12l[?25hc[?25l[?12l[?25ho[?25l[?12l[?25hl[?25l[?12l[?25ho[?25l[?12l[?25hr[?25l[?12l[?25hs[?25l[?12l[?25hh[?25l[?12l[?25h[?25l[57;8H[K[57;8H[?12l[?25hc[?25l[?12l[?25hh[?25l[?12l[?25he[?25l[?12l[?25hm[?25l[?12l[?25he[?25l[?12l[?25h[?25l [?12l[?25h...[?25lTomorrow-Night-Bright[?12l[?25h...[?25l[57;14Hblue[57;18H[K[57;18H[?12l[?25h...[?25l[57;14Hdarkblue[?12l[?25h[?25l[57;21H[K[57;21H[?12l[?25h[?25l[57;20H[K[57;20H[?12l[?25h[?25l[57;19H[K[57;19H[?12l[?25h[?25l[57;18H[K[57;18H[?12l[?25h[?25l[57;17H[K[57;17H[?12l[?25h[?25l[57;16H[K[57;16H[?12l[?25h[?25l[57;15H[K[57;15H[?12l[?25h[?25l[57;14H[K[57;14H[?12l[?25ht[?25l[?12l[?25h...[?25lTomorrow-Night-Bright[?12l[?25h...[?25l[57;14Htende[57;20H[K[57;20H[?12l[?25h
[?25l[38;5;255m[48;5;235m[27m[23m[m[38;2;238;238;238m[48;2;40;40;40m[H[2J[1;1H[38;2;68;68;68m   1 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m// Copyright 2016 Joe Wilm, The Alacritty Project Contributors[m[38;2;238;238;238m[48;2;40;40;40m[38C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m   2 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m//[m[38;2;238;238;238m[48;2;40;40;40m[98C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m   3 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m// Licensed under the Apache License, Version 2.0 (the "License");[m[38;2;238;238;238m[48;2;40;40;40m[34C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m   4 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m// you may not use this file except in compliance with the License.[m[38;2;238;238;238m[48;2;40;40;40m[33C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m   5 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m// You may obtain a copy of the License at[m[38;2;238;238;238m[48;2;40;40;40m[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m   6 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m//[m[38;2;238;238;238m[48;2;40;40;40m[98C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m   7 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m//     http://www.apache.org/licenses/LICENSE-2.0[m[38;2;238;238;238m[48;2;40;40;40m[51C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m   8 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m//[m[38;2;238;238;238m[48;2;40;40;40m[98C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m   9 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m// Unless required by applicable law or agreed to in writing, software[m[38;2;238;238;238m[48;2;40;40;40m[30C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  10 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m// distributed under the License is distributed on an "AS IS" BASIS,[m[38;2;238;238;238m[48;2;40;40;40m[32C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  11 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.[m[38;2;238;238;238m[48;2;40;40;40m[25C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  12 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m// See the License for the specific language governing permissions and[m[38;2;238;238;238m[48;2;40;40;40m[30C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  13 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m// limitations under the License.[m[38;2;238;238;238m[48;2;40;40;40m[67C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  14 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mstd[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mcollections[m[38;2;238;238;238m[48;2;40;40;40m::HashMap;[70C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  15 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mstd[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mhash[m[38;2;238;238;238m[48;2;40;40;40m::BuildHasherDefault;[66C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  16 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mstd[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mfs[m[38;2;238;238;238m[48;2;40;40;40m::File;[82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  17 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mstd[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mio[m[38;2;238;238;238m[48;2;40;40;40m::{self, Read};[74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  18 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mstd[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mmem[m[38;2;238;238;238m[48;2;40;40;40m::size_of;[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  19 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mstd[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mpath[m[38;2;238;238;238m[48;2;40;40;40m::{PathBuf};[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  20 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mstd[m[38;2;238;238;238m[48;2;40;40;40m::ptr;[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  21 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mstd[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92msync[m[38;2;238;238;238m[48;2;40;40;40m::mpsc;[80C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  22 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  23 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m cgmath;[89C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  24 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mfnv[m[38;2;238;238;238m[48;2;40;40;40m::FnvHasher;[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  25 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::{self, Rasterizer, Rasterize, RasterizedGlyph, FontDesc, GlyphKey, FontKey};[14C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  26 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mtypes[m[38;2;238;238;238m[48;2;40;40;40m::*;[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  27 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m gl;[93C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  28 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mindex[m[38;2;238;238;238m[48;2;40;40;40m::{Line, Column, RangeInclusive};[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  29 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mnotify[m[38;2;238;238;238m[48;2;40;40;40m::{Watcher [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m WatcherApi, RecommendedWatcher [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m Watcher, op};[29C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  30 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  31 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mconfig[m[38;2;238;238;238m[48;2;40;40;40m::{self, Config, Delta};[66C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  32 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mterm[m[38;2;238;238;238m[48;2;40;40;40m::{self, cell, RenderableCell};[61C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  33 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mwindow[m[38;2;238;238;238m[48;2;40;40;40m::{Size, Pixels};[73C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  34 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  35 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239muse[m[38;2;238;238;238m[48;2;40;40;40m Rgb;[92C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  36 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  37 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m// Shader paths for live reload[m[38;2;238;238;238m[48;2;40;40;40m[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  38 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mstatic[m[38;2;238;238;238m[48;2;40;40;40m TEXT_SHADER_F_PATH: &'static [38;2;115;206;244mstr[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mconcat![m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92menv![m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"CARGO_MANIFEST_DIR"[m[38;2;238;238;238m[48;2;40;40;40m), [38;2;211;185;135m"/res/text.f.glsl"[m[38;2;238;238;238m[48;2;40;40;40m);  [48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  39 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mstatic[m[38;2;238;238;238m[48;2;40;40;40m TEXT_SHADER_V_PATH: &'static [38;2;115;206;244mstr[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mconcat![m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92menv![m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"CARGO_MANIFEST_DIR"[m[38;2;238;238;238m[48;2;40;40;40m), [38;2;211;185;135m"/res/text.v.glsl"[m[38;2;238;238;238m[48;2;40;40;40m);  [48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  40 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  41 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m// Shader source which is used when live-shader-reload feature is disable[m[38;2;238;238;238m[48;2;40;40;40m[27C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  42 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mstatic[m[38;2;238;238;238m[48;2;40;40;40m TEXT_SHADER_F: &'static [38;2;115;206;244mstr[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92minclude_str![m[38;2;238;238;238m[48;2;40;40;40m([50C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  43 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;201;208;92mconcat![m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92menv![m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"CARGO_MANIFEST_DIR"[m[38;2;238;238;238m[48;2;40;40;40m), [38;2;211;185;135m"/res/text.f.glsl"[m[38;2;238;238;238m[48;2;40;40;40m)[41C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  44 [m[38;2;238;238;238m[48;2;40;40;40m);[98C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  45 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mstatic[m[38;2;238;238;238m[48;2;40;40;40m TEXT_SHADER_V: &'static [38;2;115;206;244mstr[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92minclude_str![m[38;2;238;238;238m[48;2;40;40;40m([50C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  46 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;201;208;92mconcat![m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92menv![m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"CARGO_MANIFEST_DIR"[m[38;2;238;238;238m[48;2;40;40;40m), [38;2;211;185;135m"/res/text.v.glsl"[m[38;2;238;238;238m[48;2;40;40;40m)[41C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  47 [m[38;2;238;238;238m[48;2;40;40;40m);[98C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  48 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  49 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m/// `LoadGlyph` allows for copying a rasterized glyph into graphics memory[m[38;2;238;238;238m[48;2;40;40;40m[26C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  50 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mtrait[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mLoadGlyph[m[38;2;238;238;238m[48;2;40;40;40m {[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  51 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// Load the rasterized glyph into GPU memory[m[38;2;238;238;238m[48;2;40;40;40m[51C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  52 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mload_glyph[m[38;2;238;238;238m[48;2;40;40;40m(&[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self, rasterized: &RasterizedGlyph) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m Glyph;[32C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  53 [m[38;2;238;238;238m[48;2;40;40;40m}[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  54 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  55 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239menum[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mMsg[m[38;2;238;238;238m[48;2;40;40;40m {[90C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  56 [m[38;2;238;238;238m[48;2;40;40;40m    ShaderReload,[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H1,1[11CTop[1;6H[?12l[?25h[?25l[57;103H2[2;6H[?12l[?25h[?25l[57;103H3[3;6H[?12l[?25h[?25l[57;103H4[4;6H[?12l[?25h[?25l[57;103H5[5;6H[?12l[?25h[?25l[57;103H6[6;6H[?12l[?25h[?25l[57;103H7[7;6H[?12l[?25h[?25l[57;103H8[8;6H[?12l[?25h[?25l[57;103H9[9;6H[?12l[?25h[?25l[57;103H10,1[10;6H[?12l[?25h[?25l[57;104H1[11;6H[?12l[?25h[?25l[57;104H2[12;6H[?12l[?25h[?25l[57;104H3[13;6H[?12l[?25h[?25l[57;104H4[14;6H[?12l[?25h[?25l[57;104H5[15;6H[?12l[?25h[?25l[57;104H6[16;6H[?12l[?25h[?25l[57;104H7[17;6H[?12l[?25h[?25l[57;104H8[18;6H[?12l[?25h[?25l[57;104H9[19;6H[?12l[?25h[?25l[57;103H20[20;6H[?12l[?25h[?25l[57;104H1[21;6H[?12l[?25h[?25l[57;104H2,0-1[22;6H[?12l[?25h[?25l[57;104H3,1  [23;6H[?12l[?25h[?25l[57;104H4[24;6H[?12l[?25h[?25l[57;104H5[25;6H[?12l[?25h[?25l[57;104H6[26;6H[?12l[?25h[?25l[57;104H7[27;6H[?12l[?25h[?25l[57;104H8[28;6H[?12l[?25h[?25l[57;104H9[29;6H[?12l[?25h[?25l[57;103H30,0-1[30;6H[?12l[?25h[?25l[57;104H1,1  [31;6H[?12l[?25h[?25l[57;104H2[32;6H[?12l[?25h[?25l[57;104H3[33;6H[?12l[?25h[?25l[57;104H4,0-1[34;6H[?12l[?25h[?25l[57;104H5,1  [35;6H[?12l[?25h[?25l[57;104H6,0-1[36;6H[?12l[?25h[?25l[57;104H7,1  [37;6H[?12l[?25h[?25l[57;104H8[38;6H[?12l[?25h[?25l[57;104H9[39;6H[?12l[?25h[?25l[57;103H40,0-1[40;6H[?12l[?25h[?25l[57;104H1,1  [41;6H[?12l[?25h[?25l[57;104H2[42;6H[?12l[?25h[?25l[57;104H3[43;6H[?12l[?25h[?25l[42;55H[1m[38;2;244;55;83m([44;6H)[m[38;2;238;238;238m[48;2;40;40;40m[57;104H4[44;6H[?12l[?25h[?25l[42;55H( [44;6H);[57;104H5[45;6H[?12l[?25h[?25l[57;104H6[46;6H[?12l[?25h[?25l[45;55H[1m[38;2;244;55;83m([47;6H)[m[38;2;238;238;238m[48;2;40;40;40m[57;104H7[47;6H[?12l[?25h[?25l[45;55H( [47;6H);[57;104H8,0-1[48;6H[?12l[?25h[?25l[57;104H9,1  [49;6H[?12l[?25h[?25l[57;103H50[50;6H[?12l[?25h[?25l[57;104H1[51;6H[?12l[?25h[?25l[57;104H2[52;6H[?12l[?25h[?25l[50;26H[1m[38;2;244;55;83m{[53;6H}[m[38;2;238;238;238m[48;2;40;40;40m[57;104H3[53;6H[?12l[?25h[?25l[50;26H{ [53;6H} [57;104H4,0-1[54;6H[?12l[?25h[?25l[57;104H5,1  [55;6H[?12l[?25h[?25l[57;104H6[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[54;15H[1m[38;2;244;55;83m{[m[38;2;238;238;238m[48;2;40;40;40m

[38;2;68;68;68m  57 [m[38;2;238;238;238m[48;2;40;40;40m[1m[38;2;244;55;83m}[m[38;2;238;238;238m[48;2;40;40;40m[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H57,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[53;15H{ [55;6H} 
[38;2;68;68;68m  58 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H58,0-1[9C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  59 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[derive([m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mDebug[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m)][m[38;2;238;238;238m[48;2;40;40;40m[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H59,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  60 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239menum[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mError[m[38;2;238;238;238m[48;2;40;40;40m {[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H60,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  61 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mShaderCreation[m[38;2;238;238;238m[48;2;40;40;40m(ShaderCreationError),[60C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H61,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[54;21H[1m[38;2;244;55;83m{[m[38;2;238;238;238m[48;2;40;40;40m

[38;2;68;68;68m  62 [m[38;2;238;238;238m[48;2;40;40;40m[1m[38;2;244;55;83m}[m[38;2;238;238;238m[48;2;40;40;40m[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H62,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[53;21H{ [55;6H} 
[38;2;68;68;68m  63 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H63,0-1[9C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  64 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mimpl[m[38;2;238;238;238m[48;2;40;40;40m ::[38;2;201;208;92mstd[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92merror[m[38;2;238;238;238m[48;2;40;40;40m::Error [38;2;201;208;92mfor[m[38;2;238;238;238m[48;2;40;40;40m Error {[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H64,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  65 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mcause[m[38;2;238;238;238m[48;2;40;40;40m(&self) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mOption[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m&::[38;2;201;208;92mstd[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92merror[m[38;2;238;238;238m[48;2;40;40;40m::Error[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m {[47C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H65,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  66 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mmatch[m[38;2;238;238;238m[48;2;40;40;40m *self {[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H66,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  67 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mError[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mShaderCreation[m[38;2;238;238;238m[48;2;40;40;40m([38;2;115;206;244mref[m[38;2;238;238;238m[48;2;40;40;40m err) [38;2;244;55;83m=>[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75mSome[m[38;2;238;238;238m[48;2;40;40;40m(err),[44C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H67,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  68 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H68,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  69 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H69,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  70 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H70,0-1[9C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  71 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mdescription[m[38;2;238;238;238m[48;2;40;40;40m(&self) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m &[38;2;115;206;244mstr[m[38;2;238;238;238m[48;2;40;40;40m {[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H71,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  72 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mmatch[m[38;2;238;238;238m[48;2;40;40;40m *self {[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H72,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  73 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mError[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mShaderCreation[m[38;2;238;238;238m[48;2;40;40;40m([38;2;115;206;244mref[m[38;2;238;238;238m[48;2;40;40;40m err) [38;2;244;55;83m=>[m[38;2;238;238;238m[48;2;40;40;40m err.[38;2;179;222;239mdescription[m[38;2;238;238;238m[48;2;40;40;40m(),[36C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H73,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  74 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H74,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  75 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H75,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[44;41H[1m[38;2;244;55;83m{[m[38;2;238;238;238m[48;2;40;40;40m[56;1H[38;2;68;68;68m  76 [m[38;2;238;238;238m[48;2;40;40;40m[1m[38;2;244;55;83m}[m[38;2;238;238;238m[48;2;40;40;40m[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H76,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[43;41H{ [55;6H} 
[38;2;68;68;68m  77 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H77,0-1[9C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  78 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mimpl[m[38;2;238;238;238m[48;2;40;40;40m ::[38;2;201;208;92mstd[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mfmt[m[38;2;238;238;238m[48;2;40;40;40m::Display [38;2;201;208;92mfor[m[38;2;238;238;238m[48;2;40;40;40m Error {[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H78,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  79 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfmt[m[38;2;238;238;238m[48;2;40;40;40m(&self, f: &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m ::[38;2;201;208;92mstd[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mfmt[m[38;2;238;238;238m[48;2;40;40;40m::Formatter) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m ::[38;2;201;208;92mstd[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mfmt[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;115;206;244mResult[m[38;2;238;238;238m[48;2;40;40;40m {[28C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H79,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  80 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mmatch[m[38;2;238;238;238m[48;2;40;40;40m *self {[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H80,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  81 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mError[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mShaderCreation[m[38;2;238;238;238m[48;2;40;40;40m([38;2;115;206;244mref[m[38;2;238;238;238m[48;2;40;40;40m err) [38;2;244;55;83m=>[m[38;2;238;238;238m[48;2;40;40;40m {[53C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H81,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  82 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;201;208;92mwrite![m[38;2;238;238;238m[48;2;40;40;40m(f, [38;2;211;185;135m"There was an error initializing the shaders: {}"[m[38;2;238;238;238m[48;2;40;40;40m, err)[19C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H82,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  83 [m[38;2;238;238;238m[48;2;40;40;40m[12C}[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H83,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  84 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H84,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  85 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H85,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[48;41H[1m[38;2;244;55;83m{[m[38;2;238;238;238m[48;2;40;40;40m[56;1H[38;2;68;68;68m  86 [m[38;2;238;238;238m[48;2;40;40;40m[1m[38;2;244;55;83m}[m[38;2;238;238;238m[48;2;40;40;40m[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H86,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[47;41H{ [55;6H} 
[38;2;68;68;68m  87 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H87,0-1[9C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  88 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mimpl[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mFrom[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mShaderCreationError[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mfor[m[38;2;238;238;238m[48;2;40;40;40m Error {[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H88,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  89 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfrom[m[38;2;238;238;238m[48;2;40;40;40m(val: ShaderCreationError) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m Error {[52C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H89,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  90 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mError[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mShaderCreation[m[38;2;238;238;238m[48;2;40;40;40m(val)[66C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H90,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  91 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H91,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[52;47H[1m[38;2;244;55;83m{[m[38;2;238;238;238m[48;2;40;40;40m[56;1H[38;2;68;68;68m  92 [m[38;2;238;238;238m[48;2;40;40;40m[1m[38;2;244;55;83m}[m[38;2;238;238;238m[48;2;40;40;40m[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H92,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[51;47H{ [55;6H} 
[38;2;68;68;68m  93 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H93,0-1[9C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  94 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H94,0-1[9C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  95 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m/// Text drawing program[m[38;2;238;238;238m[48;2;40;40;40m[76C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H95,1[11C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  96 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m///[m[38;2;238;238;238m[48;2;40;40;40m[97C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H96,1[11C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  97 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m/// Uniforms are prefixed with "u", and vertex attributes are prefixed with "a".[m[38;2;238;238;238m[48;2;40;40;40m[20C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H97,1[11C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  98 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[derive([m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mDebug[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m)][m[38;2;238;238;238m[48;2;40;40;40m[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H98,1[11C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m  99 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mShaderProgram[m[38;2;238;238;238m[48;2;40;40;40m {[74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H99,1[11C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 100 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// Program id[m[38;2;238;238;238m[48;2;40;40;40m[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H100,1[10C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 101 [m[38;2;238;238;238m[48;2;40;40;40m    id: GLuint,[85C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H101,1[10C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 102 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H102,0-1[8C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 103 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// projection matrix uniform[m[38;2;238;238;238m[48;2;40;40;40m[67C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H103,1[10C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 104 [m[38;2;238;238;238m[48;2;40;40;40m    u_projection: GLint,[76C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H104,1[10C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 105 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H105,0-1[8C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 106 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// Terminal dimensions (pixels)[m[38;2;238;238;238m[48;2;40;40;40m[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H106,1[10C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 107 [m[38;2;238;238;238m[48;2;40;40;40m    u_term_dim: GLint,[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H107,1[10C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 108 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H108,0-1[8C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 109 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// Cell dimensions (pixels)[m[38;2;238;238;238m[48;2;40;40;40m[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H109,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 110 [m[38;2;238;238;238m[48;2;40;40;40m    u_cell_dim: GLint,[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H110,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 111 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H111,0-1[8C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 112 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// Visual bell[m[38;2;238;238;238m[48;2;40;40;40m[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H112,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 113 [m[38;2;238;238;238m[48;2;40;40;40m    u_visual_bell: GLint,[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H113,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 114 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H114,0-1[8C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 115 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// Background pass flag[m[38;2;238;238;238m[48;2;40;40;40m[72C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H115,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 116 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m///[m[38;2;238;238;238m[48;2;40;40;40m[93C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H116,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 117 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// Rendering is split into two passes; 1 for backgrounds, and one for text[m[38;2;238;238;238m[48;2;40;40;40m[21C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H117,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 118 [m[38;2;238;238;238m[48;2;40;40;40m    u_background: GLint,[76C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H118,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 119 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H119,0-1[8C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 120 [m[38;2;238;238;238m[48;2;40;40;40m    padding_x: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H120,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 121 [m[38;2;238;238;238m[48;2;40;40;40m    padding_y: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H121,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[33;31H[1m[38;2;244;55;83m{[m[38;2;238;238;238m[48;2;40;40;40m[56;1H[38;2;68;68;68m 122 [m[38;2;238;238;238m[48;2;40;40;40m[1m[38;2;244;55;83m}[m[38;2;238;238;238m[48;2;40;40;40m[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H122,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[32;31H{ [55;6H} 
[38;2;68;68;68m 123 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H123,0-1[8C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 124 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H124,0-1[8C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 125 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[derive([m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mDebug[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m, [m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mClone[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m)][m[38;2;238;238;238m[48;2;40;40;40m[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H125,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 126 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mGlyph[m[38;2;238;238;238m[48;2;40;40;40m {[82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H126,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 127 [m[38;2;238;238;238m[48;2;40;40;40m    tex_id: GLuint,[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H127,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 128 [m[38;2;238;238;238m[48;2;40;40;40m    top: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H128,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 129 [m[38;2;238;238;238m[48;2;40;40;40m    left: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[86C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H129,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 130 [m[38;2;238;238;238m[48;2;40;40;40m    width: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[85C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H130,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 131 [m[38;2;238;238;238m[48;2;40;40;40m    height: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H131,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 132 [m[38;2;238;238;238m[48;2;40;40;40m    uv_bot: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H132,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 133 [m[38;2;238;238;238m[48;2;40;40;40m    uv_left: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H133,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 134 [m[38;2;238;238;238m[48;2;40;40;40m    uv_width: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H134,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 135 [m[38;2;238;238;238m[48;2;40;40;40m    uv_height: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H135,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[46;23H[1m[38;2;244;55;83m{[m[38;2;238;238;238m[48;2;40;40;40m[56;1H[38;2;68;68;68m 136 [m[38;2;238;238;238m[48;2;40;40;40m[1m[38;2;244;55;83m}[m[38;2;238;238;238m[48;2;40;40;40m[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H136,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[45;23H{ [55;6H} 
[38;2;68;68;68m 137 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H137,0-1[8C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 138 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m/// Naïve glyph cache[m[38;2;238;238;238m[48;2;40;40;40m[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H138,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 139 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m///[m[38;2;238;238;238m[48;2;40;40;40m[97C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H139,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 140 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m/// Currently only keyed by `char`, and thus not possible to hold different[m[38;2;238;238;238m[48;2;40;40;40m[25C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H140,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 141 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m/// representations of the same code point.[m[38;2;238;238;238m[48;2;40;40;40m[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H141,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 142 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mGlyphCache[m[38;2;238;238;238m[48;2;40;40;40m {[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H142,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 143 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// Cache of buffered glyphs[m[38;2;238;238;238m[48;2;40;40;40m[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H143,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 144 [m[38;2;238;238;238m[48;2;40;40;40m    cache: HashMap[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mGlyphKey, Glyph, BuildHasherDefault[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mFnvHasher[38;2;244;55;83m>>[m[38;2;238;238;238m[48;2;40;40;40m,[33C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H144,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 145 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H145,0-1[8C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 146 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// Rasterizer for loading new glyphs[m[38;2;238;238;238m[48;2;40;40;40m[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H146,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 147 [m[38;2;238;238;238m[48;2;40;40;40m    rasterizer: Rasterizer,[73C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H147,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 148 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H148,0-1[8C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 149 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// regular font[m[38;2;238;238;238m[48;2;40;40;40m[80C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H149,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 150 [m[38;2;238;238;238m[48;2;40;40;40m    font_key: FontKey,[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H150,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 151 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H151,0-1[8C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 152 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// italic font[m[38;2;238;238;238m[48;2;40;40;40m[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H152,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 153 [m[38;2;238;238;238m[48;2;40;40;40m    italic_key: FontKey,[76C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H153,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 154 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H154,0-1[8C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 155 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// bold font[m[38;2;238;238;238m[48;2;40;40;40m[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H155,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 156 [m[38;2;238;238;238m[48;2;40;40;40m    bold_key: FontKey,[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H156,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 157 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H157,0-1[8C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 158 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// font size[m[38;2;238;238;238m[48;2;40;40;40m[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H158,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 159 [m[38;2;238;238;238m[48;2;40;40;40m    font_size: [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::Size,[74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H159,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 160 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H160,0-1[8C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 161 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// glyph offset[m[38;2;238;238;238m[48;2;40;40;40m[80C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H161,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 162 [m[38;2;238;238;238m[48;2;40;40;40m    glyph_offset: Delta,[76C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H162,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 163 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H163,0-1[8C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 164 [m[38;2;238;238;238m[48;2;40;40;40m    metrics: ::[38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::Metrics,[71C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H164,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[33;28H[1m[38;2;244;55;83m{[m[38;2;238;238;238m[48;2;40;40;40m[56;1H[38;2;68;68;68m 165 [m[38;2;238;238;238m[48;2;40;40;40m[1m[38;2;244;55;83m}[m[38;2;238;238;238m[48;2;40;40;40m[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H165,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[32;28H{ [55;6H} 
[38;2;68;68;68m 166 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H166,0-1[8C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 167 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mimpl[m[38;2;238;238;238m[48;2;40;40;40m GlyphCache {[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H167,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 168 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mL[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m([82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H168,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 169 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m rasterizer: Rasterizer,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H169,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 170 [m[38;2;238;238;238m[48;2;40;40;40m[8Cconfig: &Config,[76C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H170,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 171 [m[38;2;238;238;238m[48;2;40;40;40m[8Cloader: &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m L[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H171,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 172 [m[38;2;238;238;238m[48;2;40;40;40m    ) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mResult[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mGlyphCache, [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::Error[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m[60C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H172,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 173 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mwhere[m[38;2;238;238;238m[48;2;40;40;40m L: LoadGlyph[74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H173,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 174 [m[38;2;238;238;238m[48;2;40;40;40m    {[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H174,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 175 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m font [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m config.[38;2;179;222;239mfont[m[38;2;238;238;238m[48;2;40;40;40m();[67C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H175,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 176 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m size [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m font.[38;2;179;222;239msize[m[38;2;238;238;238m[48;2;40;40;40m();[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H176,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 177 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m glyph_offset [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m *font.[38;2;179;222;239mglyph_offset[m[38;2;238;238;238m[48;2;40;40;40m();[52C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H177,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 178 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H178,0-1[8C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 179 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mmake_desc[m[38;2;238;238;238m[48;2;40;40;40m([79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H179,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 180 [m[38;2;238;238;238m[48;2;40;40;40m[12Cdesc: &[38;2;201;208;92mconfig[m[38;2;238;238;238m[48;2;40;40;40m::FontDescription,[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H180,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 181 [m[38;2;238;238;238m[48;2;40;40;40m[12Cslant: [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::Slant,[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H181,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 182 [m[38;2;238;238;238m[48;2;40;40;40m[12Cweight: [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::Weight,[67C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H182,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 183 [m[38;2;238;238;238m[48;2;40;40;40m[8C) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m FontDesc[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H183,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 184 [m[38;2;238;238;238m[48;2;40;40;40m[8C{[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H184,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 185 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m style [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75mSome[m[38;2;238;238;238m[48;2;40;40;40m([38;2;115;206;244mref[m[38;2;238;238;238m[48;2;40;40;40m spec) [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m desc.style {[40C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H185,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 186 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mStyle[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mSpecific[m[38;2;238;238;238m[48;2;40;40;40m(spec.[38;2;179;222;239mto_owned[m[38;2;238;238;238m[48;2;40;40;40m())[46C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H186,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 187 [m[38;2;238;238;238m[48;2;40;40;40m[12C} [38;2;201;208;92melse[m[38;2;238;238;238m[48;2;40;40;40m {[80C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H187,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 188 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mStyle[m[38;2;238;238;238m[48;2;40;40;40m::Description {slant:slant, weight:weight}[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H188,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 189 [m[38;2;238;238;238m[48;2;40;40;40m[12C};[86C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H189,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 190 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mFontDesc[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m(&desc.family[..], style)[50C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H190,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 191 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H191,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 192 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H192,0-1[7C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 193 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;102;102;102m// Load regular font[m[38;2;238;238;238m[48;2;40;40;40m[72C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H193,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 194 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m regular_desc [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mmake_desc[m[38;2;238;238;238m[48;2;40;40;40m(&font.normal, [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mSlant[m[38;2;238;238;238m[48;2;40;40;40m::Normal, [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mWeight[m[38;2;238;238;238m[48;2;40;40;40m::Normal);      [48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H194,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 195 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H195,0-1[7C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 196 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m regular [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m rasterizer[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H196,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 197 [m[38;2;238;238;238m[48;2;40;40;40m[12C.[38;2;179;222;239mload_font[m[38;2;238;238;238m[48;2;40;40;40m(&regular_desc, size)[38;2;244;55;83m?[m[38;2;238;238;238m[48;2;40;40;40m;[55C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H197,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 198 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H198,0-1[7C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 199 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;102;102;102m// helper to load a description if it is not the regular_desc[m[38;2;238;238;238m[48;2;40;40;40m[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H199,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 200 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m load_or_regular [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m|[m[38;2;238;238;238m[48;2;40;40;40mdesc:FontDesc, rasterizer: &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m Rasterizer[38;2;244;55;83m|[m[38;2;238;238;238m[48;2;40;40;40m {[24C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H200,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 201 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m desc [38;2;244;55;83m==[m[38;2;238;238;238m[48;2;40;40;40m regular_desc {[63C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H201,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 202 [m[38;2;238;238;238m[48;2;40;40;40m[16Cregular[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H202,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 203 [m[38;2;238;238;238m[48;2;40;40;40m[12C} [38;2;201;208;92melse[m[38;2;238;238;238m[48;2;40;40;40m {[80C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H203,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 204 [m[38;2;238;238;238m[48;2;40;40;40m[16Crasterizer.[38;2;179;222;239mload_font[m[38;2;238;238;238m[48;2;40;40;40m(&desc, size).[38;2;179;222;239munwrap_or_else[m[38;2;238;238;238m[48;2;40;40;40m([38;2;244;55;83m|[m[38;2;238;238;238m[48;2;40;40;40m_[38;2;244;55;83m|[m[38;2;238;238;238m[48;2;40;40;40m regular)[23C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H204,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 205 [m[38;2;238;238;238m[48;2;40;40;40m[12C}[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H205,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 206 [m[38;2;238;238;238m[48;2;40;40;40m[8C};[90C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H206,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 207 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H207,0-1[7C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 208 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;102;102;102m// Load bold font[m[38;2;238;238;238m[48;2;40;40;40m[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H208,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 209 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m bold_desc [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mmake_desc[m[38;2;238;238;238m[48;2;40;40;40m(&font.bold, [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mSlant[m[38;2;238;238;238m[48;2;40;40;40m::Normal, [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mWeight[m[38;2;238;238;238m[48;2;40;40;40m::Bold);[13C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H209,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 210 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H210,0-1[7C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 211 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m bold [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mload_or_regular[m[38;2;238;238;238m[48;2;40;40;40m(bold_desc, &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m rasterizer);[37C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H211,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 212 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H212,0-1[7C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 213 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;102;102;102m// Load italic font[m[38;2;238;238;238m[48;2;40;40;40m[73C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H213,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 214 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m italic_desc [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mmake_desc[m[38;2;238;238;238m[48;2;40;40;40m(&font.italic, [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mSlant[m[38;2;238;238;238m[48;2;40;40;40m::Italic, [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mWeight[m[38;2;238;238;238m[48;2;40;40;40m::Normal);[7C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H214,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 215 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H215,0-1[7C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 216 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m italic [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mload_or_regular[m[38;2;238;238;238m[48;2;40;40;40m(italic_desc, &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m rasterizer);[33C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H216,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 217 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H217,0-1[7C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 218 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;102;102;102m// Need to load at least one glyph for the face before calling metrics.[m[38;2;238;238;238m[48;2;40;40;40m[21C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H218,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 219 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;102;102;102m// The glyph requested here ('m' at the time of writing) has no special[m[38;2;238;238;238m[48;2;40;40;40m[21C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H219,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 220 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;102;102;102m// meaning.[m[38;2;238;238;238m[48;2;40;40;40m[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H220,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 221 [m[38;2;238;238;238m[48;2;40;40;40m[8Crasterizer.[38;2;179;222;239mget_glyph[m[38;2;238;238;238m[48;2;40;40;40m(&GlyphKey { font_key: regular, c: [38;2;255;194;75m'm'[m[38;2;238;238;238m[48;2;40;40;40m, size: font.[38;2;179;222;239msize[m[38;2;238;238;238m[48;2;40;40;40m() })[38;2;244;55;83m?[m[38;2;238;238;238m[48;2;40;40;40m;[10C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H221,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 222 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m metrics [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m rasterizer.[38;2;179;222;239mmetrics[m[38;2;238;238;238m[48;2;40;40;40m(regular)[38;2;244;55;83m?[m[38;2;238;238;238m[48;2;40;40;40m;[49C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H222,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 223 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H223,0-1[7C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 224 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m cache [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m GlyphCache {[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H224,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 225 [m[38;2;238;238;238m[48;2;40;40;40m[12Ccache: [38;2;201;208;92mHashMap[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mdefault[m[38;2;238;238;238m[48;2;40;40;40m(),[62C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H225,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 226 [m[38;2;238;238;238m[48;2;40;40;40m[12Crasterizer: rasterizer,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H226,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 227 [m[38;2;238;238;238m[48;2;40;40;40m[12Cfont_size: font.[38;2;179;222;239msize[m[38;2;238;238;238m[48;2;40;40;40m(),[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H227,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 228 [m[38;2;238;238;238m[48;2;40;40;40m[12Cfont_key: regular,[70C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H228,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 229 [m[38;2;238;238;238m[48;2;40;40;40m[12Cbold_key: bold,[73C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H229,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 230 [m[38;2;238;238;238m[48;2;40;40;40m[12Citalic_key: italic,[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H230,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 231 [m[38;2;238;238;238m[48;2;40;40;40m[12Cglyph_offset: glyph_offset,[61C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H231,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 232 [m[38;2;238;238;238m[48;2;40;40;40m[12Cmetrics: metrics[72C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H232,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 233 [m[38;2;238;238;238m[48;2;40;40;40m[8C};[90C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H233,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 234 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H234,0-1[7C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 235 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mmacro_rules![m[38;2;238;238;238m[48;2;40;40;40m load_glyphs_for_font {[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H235,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 236 [m[38;2;238;238;238m[48;2;40;40;40m[12C([38;2;201;208;92m$font[m[38;2;238;238;238m[48;2;40;40;40m:expr) [38;2;244;55;83m=>[m[38;2;238;238;238m[48;2;40;40;40m {[71C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H236,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 237 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;201;208;92mfor[m[38;2;238;238;238m[48;2;40;40;40m i [38;2;179;222;239min[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mRangeInclusive[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m32u8[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m128u8[m[38;2;238;238;238m[48;2;40;40;40m) {[41C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H237,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 238 [m[38;2;238;238;238m[48;2;40;40;40m[20Ccache.[38;2;179;222;239mget[m[38;2;238;238;238m[48;2;40;40;40m(&GlyphKey {[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H238,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 239 [m[38;2;238;238;238m[48;2;40;40;40m[24Cfont_key: [38;2;201;208;92m$font[m[38;2;238;238;238m[48;2;40;40;40m,[60C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H239,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 240 [m[38;2;238;238;238m[48;2;40;40;40m[24Cc: i [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mchar[m[38;2;238;238;238m[48;2;40;40;40m,[63C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H240,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 241 [m[38;2;238;238;238m[48;2;40;40;40m[24Csize: font.[38;2;179;222;239msize[m[38;2;238;238;238m[48;2;40;40;40m()[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H241,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 242 [m[38;2;238;238;238m[48;2;40;40;40m[20C}, loader);[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H242,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 243 [m[38;2;238;238;238m[48;2;40;40;40m[16C}[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H243,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 244 [m[38;2;238;238;238m[48;2;40;40;40m[12C}[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H244,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 245 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H245,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 246 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H246,0-1[7C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 247 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mload_glyphs_for_font![m[38;2;238;238;238m[48;2;40;40;40m(regular);[61C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H247,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 248 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mload_glyphs_for_font![m[38;2;238;238;238m[48;2;40;40;40m(bold);[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H248,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 249 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mload_glyphs_for_font![m[38;2;238;238;238m[48;2;40;40;40m(italic);[62C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H249,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 250 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H250,0-1[7C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 251 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;255;194;75mOk[m[38;2;238;238;238m[48;2;40;40;40m(cache)[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H251,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 252 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H252,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 253 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H253,0-1[7C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 254 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfont_metrics[m[38;2;238;238;238m[48;2;40;40;40m(&self) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::Metrics {[51C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H254,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 255 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.rasterizer[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H255,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 256 [m[38;2;238;238;238m[48;2;40;40;40m[12C.[38;2;179;222;239mmetrics[m[38;2;238;238;238m[48;2;40;40;40m(self.font_key)[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H256,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 257 [m[38;2;238;238;238m[48;2;40;40;40m[12C.[38;2;179;222;239mexpect[m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"metrics load since font is loaded at glyph cache creation"[m[38;2;238;238;238m[48;2;40;40;40m)[20C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H257,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 258 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H258,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 259 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H259,0-1[7C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 260 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mget[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m'a, L[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m(&'a [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self, glyph_key: &GlyphKey, loader: &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m L) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m &'a Glyph[14C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H260,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 261 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mwhere[m[38;2;238;238;238m[48;2;40;40;40m L: LoadGlyph[74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H261,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 262 [m[38;2;238;238;238m[48;2;40;40;40m    {[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H262,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 263 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m glyph_offset [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m self.glyph_offset;[55C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H263,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 264 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m rasterizer [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self.rasterizer;[54C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H264,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 265 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m metrics [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m &self.metrics;[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H265,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 266 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.cache[82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H266,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 267 [m[38;2;238;238;238m[48;2;40;40;40m[12C.[38;2;179;222;239mentry[m[38;2;238;238;238m[48;2;40;40;40m(*glyph_key)[70C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H267,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 268 [m[38;2;238;238;238m[48;2;40;40;40m[12C.[38;2;179;222;239mor_insert_with[m[38;2;238;238;238m[48;2;40;40;40m([38;2;244;55;83m||[m[38;2;238;238;238m[48;2;40;40;40m {[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H268,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 269 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m rasterized [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m rasterizer.[38;2;179;222;239mget_glyph[m[38;2;238;238;238m[48;2;40;40;40m(&glyph_key)[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H269,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 270 [m[38;2;238;238;238m[48;2;40;40;40m[20C.[38;2;179;222;239munwrap_or_else[m[38;2;238;238;238m[48;2;40;40;40m([38;2;244;55;83m|[m[38;2;238;238;238m[48;2;40;40;40m_[38;2;244;55;83m|[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mDefault[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mdefault[m[38;2;238;238;238m[48;2;40;40;40m());[40C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H270,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 271 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H271,0-1[7C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 272 [m[38;2;238;238;238m[48;2;40;40;40m[16Crasterized.left [38;2;244;55;83m+=[m[38;2;238;238;238m[48;2;40;40;40m glyph_offset.x [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m;[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H272,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 273 [m[38;2;238;238;238m[48;2;40;40;40m[16Crasterized.top [38;2;244;55;83m+=[m[38;2;238;238;238m[48;2;40;40;40m glyph_offset.y [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m;[44C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H273,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 274 [m[38;2;238;238;238m[48;2;40;40;40m[16Crasterized.top [38;2;244;55;83m-=[m[38;2;238;238;238m[48;2;40;40;40m metrics.descent [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m;[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H274,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 275 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H275,0-1[7C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 276 [m[38;2;238;238;238m[48;2;40;40;40m[16Cloader.[38;2;179;222;239mload_glyph[m[38;2;238;238;238m[48;2;40;40;40m(&rasterized)[54C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H276,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 277 [m[38;2;238;238;238m[48;2;40;40;40m[12C})[86C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H277,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 278 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H278,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 279 [m[38;2;238;238;238m[48;2;40;40;40m}[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H279,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 280 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H280,0-1[7C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 281 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[derive([m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mDebug[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m)][m[38;2;238;238;238m[48;2;40;40;40m[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H281,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 282 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[repr(C)][m[38;2;238;238;238m[48;2;40;40;40m[90C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H282,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 283 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mInstanceData[m[38;2;238;238;238m[48;2;40;40;40m {[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H283,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 284 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// coords[m[38;2;238;238;238m[48;2;40;40;40m[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H284,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 285 [m[38;2;238;238;238m[48;2;40;40;40m    col: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H285,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 286 [m[38;2;238;238;238m[48;2;40;40;40m    row: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H286,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 287 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// glyph offset[m[38;2;238;238;238m[48;2;40;40;40m[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H287,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 288 [m[38;2;238;238;238m[48;2;40;40;40m    left: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[86C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H288,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 289 [m[38;2;238;238;238m[48;2;40;40;40m    top: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H289,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 290 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// glyph scale[m[38;2;238;238;238m[48;2;40;40;40m[82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H290,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 291 [m[38;2;238;238;238m[48;2;40;40;40m    width: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[85C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H291,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 292 [m[38;2;238;238;238m[48;2;40;40;40m    height: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H292,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 293 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// uv offset[m[38;2;238;238;238m[48;2;40;40;40m[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H293,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 294 [m[38;2;238;238;238m[48;2;40;40;40m    uv_left: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H294,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 295 [m[38;2;238;238;238m[48;2;40;40;40m    uv_bot: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H295,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 296 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// uv scale[m[38;2;238;238;238m[48;2;40;40;40m[85C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H296,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 297 [m[38;2;238;238;238m[48;2;40;40;40m    uv_width: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H297,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 298 [m[38;2;238;238;238m[48;2;40;40;40m    uv_height: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H298,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 299 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// [m[38;2;238;238;238m[48;2;40;40;40m[1m[4m[38;2;255;255;255mcolor[m[38;2;238;238;238m[48;2;40;40;40m[88C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H299,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 300 [m[38;2;238;238;238m[48;2;40;40;40m    r: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[89C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H300,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 301 [m[38;2;238;238;238m[48;2;40;40;40m    g: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[89C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H301,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 302 [m[38;2;238;238;238m[48;2;40;40;40m    b: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[89C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H302,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 303 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// background [m[38;2;238;238;238m[48;2;40;40;40m[1m[4m[38;2;255;255;255mcolor[m[38;2;238;238;238m[48;2;40;40;40m[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H303,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 304 [m[38;2;238;238;238m[48;2;40;40;40m    bg_r: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[86C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H304,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 305 [m[38;2;238;238;238m[48;2;40;40;40m    bg_g: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[86C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H305,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 306 [m[38;2;238;238;238m[48;2;40;40;40m    bg_b: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[86C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H306,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[32;26H[1m[38;2;244;55;83m{[m[38;2;238;238;238m[48;2;40;40;40m[56;1H[38;2;68;68;68m 307 [m[38;2;238;238;238m[48;2;40;40;40m[1m[38;2;244;55;83m}[m[38;2;238;238;238m[48;2;40;40;40m[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H307,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[31;26H{ [55;6H} 
[38;2;68;68;68m 308 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H308,0-1[7C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 309 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[derive([m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mDebug[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m)][m[38;2;238;238;238m[48;2;40;40;40m[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H309,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 310 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mQuadRenderer[m[38;2;238;238;238m[48;2;40;40;40m {[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H310,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 311 [m[38;2;238;238;238m[48;2;40;40;40m    program: ShaderProgram,[73C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H311,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 312 [m[38;2;238;238;238m[48;2;40;40;40m    vao: GLuint,[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H312,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 313 [m[38;2;238;238;238m[48;2;40;40;40m    vbo: GLuint,[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H313,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 314 [m[38;2;238;238;238m[48;2;40;40;40m    ebo: GLuint,[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H314,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 315 [m[38;2;238;238;238m[48;2;40;40;40m    vbo_instance: GLuint,[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H315,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 316 [m[38;2;238;238;238m[48;2;40;40;40m    atlas: [38;2;115;206;244mVec[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mAtlas[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m,[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H316,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 317 [m[38;2;238;238;238m[48;2;40;40;40m    active_tex: GLuint,[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H317,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 318 [m[38;2;238;238;238m[48;2;40;40;40m    batch: Batch,[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H318,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 319 [m[38;2;238;238;238m[48;2;40;40;40m    rx: [38;2;201;208;92mmpsc[m[38;2;238;238;238m[48;2;40;40;40m::Receiver[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mMsg[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m,[72C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H319,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[46;30H[1m[38;2;244;55;83m{[m[38;2;238;238;238m[48;2;40;40;40m[56;1H[38;2;68;68;68m 320 [m[38;2;238;238;238m[48;2;40;40;40m[1m[38;2;244;55;83m}[m[38;2;238;238;238m[48;2;40;40;40m[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H320,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[45;30H{ [55;6H} 
[38;2;68;68;68m 321 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H321,0-1[7C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 322 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[derive([m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mDebug[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m)][m[38;2;238;238;238m[48;2;40;40;40m[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H322,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 323 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mRenderApi[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m'a[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m {[74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H323,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 324 [m[38;2;238;238;238m[48;2;40;40;40m    active_tex: &'a [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m GLuint,[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H324,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 325 [m[38;2;238;238;238m[48;2;40;40;40m    batch: &'a [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m Batch,[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H325,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 326 [m[38;2;238;238;238m[48;2;40;40;40m    atlas: &'a [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mVec[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mAtlas[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m,[70C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H326,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 327 [m[38;2;238;238;238m[48;2;40;40;40m    program: &'a [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m ShaderProgram,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H327,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 328 [m[38;2;238;238;238m[48;2;40;40;40m    config: &'a Config,[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H328,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 329 [m[38;2;238;238;238m[48;2;40;40;40m    visual_bell_intensity: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m[70C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H329,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[49;31H[1m[38;2;244;55;83m{[m[38;2;238;238;238m[48;2;40;40;40m[56;1H[38;2;68;68;68m 330 [m[38;2;238;238;238m[48;2;40;40;40m[1m[38;2;244;55;83m}[m[38;2;238;238;238m[48;2;40;40;40m[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H330,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[48;31H{ [55;6H} 
[38;2;68;68;68m 331 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H331,0-1[7C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 332 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[derive([m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mDebug[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m)][m[38;2;238;238;238m[48;2;40;40;40m[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H332,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 333 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mLoaderApi[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m'a[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m {[74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H333,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 334 [m[38;2;238;238;238m[48;2;40;40;40m    active_tex: &'a [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m GLuint,[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H334,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 335 [m[38;2;238;238;238m[48;2;40;40;40m    atlas: &'a [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mVec[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mAtlas[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m,[70C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H335,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[53;31H[1m[38;2;244;55;83m{[m[38;2;238;238;238m[48;2;40;40;40m


[38;2;68;68;68m 336 [m[38;2;238;238;238m[48;2;40;40;40m[1m[38;2;244;55;83m}[m[38;2;238;238;238m[48;2;40;40;40m[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H336,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[52;31H{ [55;6H} 
[38;2;68;68;68m 337 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H337,0-1[7C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 338 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[derive([m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mDebug[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m)][m[38;2;238;238;238m[48;2;40;40;40m[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H338,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 339 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mPackedVertex[m[38;2;238;238;238m[48;2;40;40;40m {[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H339,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 340 [m[38;2;238;238;238m[48;2;40;40;40m    x: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[89C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H340,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 341 [m[38;2;238;238;238m[48;2;40;40;40m    y: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[89C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H341,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[53;30H[1m[38;2;244;55;83m{[m[38;2;238;238;238m[48;2;40;40;40m


[38;2;68;68;68m 342 [m[38;2;238;238;238m[48;2;40;40;40m[1m[38;2;244;55;83m}[m[38;2;238;238;238m[48;2;40;40;40m[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H342,1[9C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[52;30H{ [55;6H} 
[38;2;68;68;68m 343 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H343,0-1[7C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 344 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[derive([m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mDebug[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m)][m[38;2;238;238;238m[48;2;40;40;40m[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H344,1[9C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 345 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mBatch[m[38;2;238;238;238m[48;2;40;40;40m {[82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H345,1[9C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 346 [m[38;2;238;238;238m[48;2;40;40;40m    tex: GLuint,[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H346,1[9C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;68;68;68m 347 [m[38;2;238;238;238m[48;2;40;40;40m    instances: [38;2;115;206;244mVec[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mInstanceData[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m,[67C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H347,1[9C22%[56;6H[?12l[?25h[?25l[1;56r[1;1H[28M[1;57r[29;1H[38;2;68;68;68m 348 [m[38;2;238;238;238m[48;2;40;40;40m}[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 349 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 350 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mimpl[m[38;2;238;238;238m[48;2;40;40;40m Batch {[88C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 351 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;201;208;92m#[inline][m[38;2;238;238;238m[48;2;40;40;40m[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 352 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m Batch {[73C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 353 [m[38;2;238;238;238m[48;2;40;40;40m[8CBatch {[85C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 354 [m[38;2;238;238;238m[48;2;40;40;40m[12Ctex: [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m,[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 355 [m[38;2;238;238;238m[48;2;40;40;40m[12Cinstances: [38;2;115;206;244mVec[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mwith_capacity[m[38;2;238;238;238m[48;2;40;40;40m(BATCH_MAX),[47C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 356 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 357 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 358 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 359 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239madd_item[m[38;2;238;238;238m[48;2;40;40;40m([80C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 360 [m[38;2;238;238;238m[48;2;40;40;40m[8C&[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self,[82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 361 [m[38;2;238;238;238m[48;2;40;40;40m[8Ccell: &RenderableCell,[70C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 362 [m[38;2;238;238;238m[48;2;40;40;40m[8Cglyph: &Glyph,[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 363 [m[38;2;238;238;238m[48;2;40;40;40m    ) {[93C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 364 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m self.[38;2;179;222;239mis_empty[m[38;2;238;238;238m[48;2;40;40;40m() {[72C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 365 [m[38;2;238;238;238m[48;2;40;40;40m[12Cself.tex [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m glyph.tex_id;[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 366 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 367 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 368 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.instances.[38;2;179;222;239mpush[m[38;2;238;238;238m[48;2;40;40;40m(InstanceData {[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 369 [m[38;2;238;238;238m[48;2;40;40;40m[12Ccol: cell.column.[38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[62C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 370 [m[38;2;238;238;238m[48;2;40;40;40m[12Crow: cell.line.[38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 371 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 372 [m[38;2;238;238;238m[48;2;40;40;40m[12Ctop: glyph.top,[73C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 373 [m[38;2;238;238;238m[48;2;40;40;40m[12Cleft: glyph.left,[71C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 374 [m[38;2;238;238;238m[48;2;40;40;40m[12Cwidth: glyph.width,[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 375 [m[38;2;238;238;238m[48;2;40;40;40m[12Cheight: glyph.height,[67C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H375,13[8C24%[56;18H[?12l[?25h[?25l[1;56r[1;1H[28M[1;57r[29;1H[38;2;68;68;68m 376 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 377 [m[38;2;238;238;238m[48;2;40;40;40m[12Cuv_bot: glyph.uv_bot,[67C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 378 [m[38;2;238;238;238m[48;2;40;40;40m[12Cuv_left: glyph.uv_left,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 379 [m[38;2;238;238;238m[48;2;40;40;40m[12Cuv_width: glyph.uv_width,[63C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 380 [m[38;2;238;238;238m[48;2;40;40;40m[12Cuv_height: glyph.uv_height,[61C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 381 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 382 [m[38;2;238;238;238m[48;2;40;40;40m[12Cr: cell.fg.r [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 383 [m[38;2;238;238;238m[48;2;40;40;40m[12Cg: cell.fg.g [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 384 [m[38;2;238;238;238m[48;2;40;40;40m[12Cb: cell.fg.b [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 385 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 386 [m[38;2;238;238;238m[48;2;40;40;40m[12Cbg_r: cell.bg.r [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 387 [m[38;2;238;238;238m[48;2;40;40;40m[12Cbg_g: cell.bg.g [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 388 [m[38;2;238;238;238m[48;2;40;40;40m[12Cbg_b: cell.bg.b [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 389 [m[38;2;238;238;238m[48;2;40;40;40m[8C});[89C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 390 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 391 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 392 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;201;208;92m#[inline][m[38;2;238;238;238m[48;2;40;40;40m[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 393 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfull[m[38;2;238;238;238m[48;2;40;40;40m(&self) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mbool[m[38;2;238;238;238m[48;2;40;40;40m {[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 394 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.[38;2;179;222;239mcapacity[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83m==[m[38;2;238;238;238m[48;2;40;40;40m self.[38;2;179;222;239mlen[m[38;2;238;238;238m[48;2;40;40;40m()[63C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 395 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 396 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 397 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;201;208;92m#[inline][m[38;2;238;238;238m[48;2;40;40;40m[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 398 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mlen[m[38;2;238;238;238m[48;2;40;40;40m(&self) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244musize[m[38;2;238;238;238m[48;2;40;40;40m {[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 399 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.instances.[38;2;179;222;239mlen[m[38;2;238;238;238m[48;2;40;40;40m()[72C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 400 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 401 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 402 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;201;208;92m#[inline][m[38;2;238;238;238m[48;2;40;40;40m[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 403 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mcapacity[m[38;2;238;238;238m[48;2;40;40;40m(&self) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244musize[m[38;2;238;238;238m[48;2;40;40;40m {[63C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H403,5[9C26%[56;10H[?12l[?25h[?25l[1;56r[1;1H[28M[1;57r[29;1H[38;2;68;68;68m 404 [m[38;2;238;238;238m[48;2;40;40;40m[8CBATCH_MAX[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 405 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 406 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 407 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;201;208;92m#[inline][m[38;2;238;238;238m[48;2;40;40;40m[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 408 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mis_empty[m[38;2;238;238;238m[48;2;40;40;40m(&self) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mbool[m[38;2;238;238;238m[48;2;40;40;40m {[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 409 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.[38;2;179;222;239mlen[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83m==[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 410 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 411 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 412 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;201;208;92m#[inline][m[38;2;238;238;238m[48;2;40;40;40m[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 413 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239msize[m[38;2;238;238;238m[48;2;40;40;40m(&self) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244musize[m[38;2;238;238;238m[48;2;40;40;40m {[67C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 414 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.[38;2;179;222;239mlen[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83m*[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mInstanceData[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m()[54C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 415 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 416 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 417 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mclear[m[38;2;238;238;238m[48;2;40;40;40m(&[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self) {[71C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 418 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.tex [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m;[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 419 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.instances.[38;2;179;222;239mclear[m[38;2;238;238;238m[48;2;40;40;40m();[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 420 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 421 [m[38;2;238;238;238m[48;2;40;40;40m}[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 422 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 423 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m/// Maximum items to be drawn in a batch.[m[38;2;238;238;238m[48;2;40;40;40m[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 424 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mconst[m[38;2;238;238;238m[48;2;40;40;40m BATCH_MAX: [38;2;115;206;244musize[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75m65_536[m[38;2;238;238;238m[48;2;40;40;40m;[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 425 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mconst[m[38;2;238;238;238m[48;2;40;40;40m ATLAS_SIZE: [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75m1024[m[38;2;238;238;238m[48;2;40;40;40m;[71C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 426 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 427 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mimpl[m[38;2;238;238;238m[48;2;40;40;40m QuadRenderer {[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 428 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// [m[38;2;238;238;238m[48;2;40;40;40mTODO[38;2;102;102;102m should probably hand this a transform instead of width/height[m[38;2;238;238;238m[48;2;40;40;40m[27C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 429 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m(config: &Config, size: Size[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mPixels[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mu32[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m>>[m[38;2;238;238;238m[48;2;40;40;40m) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mResult[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mQuadRenderer, Error[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m {[11C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 430 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m program [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mShaderProgram[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m(config, size)[38;2;244;55;83m?[m[38;2;238;238;238m[48;2;40;40;40m;[44C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 431 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H431,0-1[7C28%[56;6H[?12l[?25h[?25l[1;56r[1;1H[28M[1;57r[29;1H[38;2;68;68;68m 432 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m vao: GLuint [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m;[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 433 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m vbo: GLuint [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m;[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 434 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m ebo: GLuint [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m;[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 435 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 436 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m vbo_instance: GLuint [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m;[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 437 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 438 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239munsafe[m[38;2;238;238;238m[48;2;40;40;40m {[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 439 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mEnable[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::BLEND);[66C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 440 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBlendFunc[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::SRC1_[1m[4m[38;2;255;255;255mCOLOR[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ONE_MINUS_SRC1_[1m[4m[38;2;255;255;255mCOLOR[m[38;2;238;238;238m[48;2;40;40;40m);[32C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 441 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mEnable[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::MULTISAMPLE);[60C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 442 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 443 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mGenVertexArrays[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m, &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m vao);[55C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 444 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mGenBuffers[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m, &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m vbo);[60C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 445 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mGenBuffers[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m, &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m ebo);[60C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 446 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mGenBuffers[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m, &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m vbo_instance);[51C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 447 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindVertexArray[m[38;2;238;238;238m[48;2;40;40;40m(vao);[63C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 448 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 449 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// ----------------------------[m[38;2;238;238;238m[48;2;40;40;40m[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 450 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// setup vertex position buffer[m[38;2;238;238;238m[48;2;40;40;40m[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 451 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// ----------------------------[m[38;2;238;238;238m[48;2;40;40;40m[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 452 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// Top right, Bottom right, Bottom left, Top left[m[38;2;238;238;238m[48;2;40;40;40m[39C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 453 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m vertices [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [[72C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 454 [m[38;2;238;238;238m[48;2;40;40;40m[16CPackedVertex { x: [38;2;255;194;75m1.0[m[38;2;238;238;238m[48;2;40;40;40m, y: [38;2;255;194;75m1.0[m[38;2;238;238;238m[48;2;40;40;40m },[52C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 455 [m[38;2;238;238;238m[48;2;40;40;40m[16CPackedVertex { x: [38;2;255;194;75m1.0[m[38;2;238;238;238m[48;2;40;40;40m, y: [38;2;255;194;75m0.0[m[38;2;238;238;238m[48;2;40;40;40m },[52C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 456 [m[38;2;238;238;238m[48;2;40;40;40m[16CPackedVertex { x: [38;2;255;194;75m0.0[m[38;2;238;238;238m[48;2;40;40;40m, y: [38;2;255;194;75m0.0[m[38;2;238;238;238m[48;2;40;40;40m },[52C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 457 [m[38;2;238;238;238m[48;2;40;40;40m[16CPackedVertex { x: [38;2;255;194;75m0.0[m[38;2;238;238;238m[48;2;40;40;40m, y: [38;2;255;194;75m1.0[m[38;2;238;238;238m[48;2;40;40;40m },[52C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 458 [m[38;2;238;238;238m[48;2;40;40;40m[12C];[86C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 459 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H459,0-1[7C31%[56;6H[?12l[?25h[?25l[1;56r[1;1H[28M[1;57r[29;1H[38;2;68;68;68m 460 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindBuffer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ARRAY_BUFFER, vbo);[50C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 461 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 462 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribPointer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m2[m[38;2;238;238;238m[48;2;40;40;40m,[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 463 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FLOAT, [38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FALSE,[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 464 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mPackedVertex[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m,[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 465 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mptr[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnull[m[38;2;238;238;238m[48;2;40;40;40m());[51C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 466 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mEnableVertexAttribArray[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m);[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 467 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 468 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBufferData[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ARRAY_BUFFER,[56C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 469 [m[38;2;238;238;238m[48;2;40;40;40m[27C([38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mPackedVertex[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83m*[m[38;2;238;238;238m[48;2;40;40;40m vertices.[38;2;179;222;239mlen[m[38;2;238;238;238m[48;2;40;40;40m()) [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m GLsizeiptr,[14C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 470 [m[38;2;238;238;238m[48;2;40;40;40m[27Cvertices.[38;2;179;222;239mas_ptr[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m *[38;2;115;206;244mconst[m[38;2;238;238;238m[48;2;40;40;40m _,[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 471 [m[38;2;238;238;238m[48;2;40;40;40m[27C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::STATIC_DRAW);[56C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 472 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 473 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// ---------------------[m[38;2;238;238;238m[48;2;40;40;40m[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 474 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// Set up element buffer[m[38;2;238;238;238m[48;2;40;40;40m[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 475 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// ---------------------[m[38;2;238;238;238m[48;2;40;40;40m[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 476 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m indices: [[38;2;115;206;244mu32[m[38;2;238;238;238m[48;2;40;40;40m; [38;2;255;194;75m6[m[38;2;238;238;238m[48;2;40;40;40m] [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [[38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m3[m[38;2;238;238;238m[48;2;40;40;40m,[55C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 477 [m[38;2;238;238;238m[48;2;40;40;40m[37C[38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m2[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m3[m[38;2;238;238;238m[48;2;40;40;40m];[54C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 478 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 479 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindBuffer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ELEMENT_ARRAY_BUFFER, ebo);[42C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 480 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBufferData[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ELEMENT_ARRAY_BUFFER,[48C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 481 [m[38;2;238;238;238m[48;2;40;40;40m[27C([38;2;255;194;75m6[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m*[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mu32[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m()) [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244misize[m[38;2;238;238;238m[48;2;40;40;40m,[41C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 482 [m[38;2;238;238;238m[48;2;40;40;40m[27Cindices.[38;2;179;222;239mas_ptr[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m *[38;2;115;206;244mconst[m[38;2;238;238;238m[48;2;40;40;40m _,[44C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 483 [m[38;2;238;238;238m[48;2;40;40;40m[27C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::STATIC_DRAW);[56C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 484 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 485 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// ----------------------------[m[38;2;238;238;238m[48;2;40;40;40m[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 486 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// Setup vertex instance buffer[m[38;2;238;238;238m[48;2;40;40;40m[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 487 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// ----------------------------[m[38;2;238;238;238m[48;2;40;40;40m[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H487,13[8C33%[56;18H[?12l[?25h[?25l[1;56r[1;1H[28M[1;57r[29;1H[38;2;68;68;68m 488 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindBuffer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ARRAY_BUFFER, vbo_instance);[41C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 489 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBufferData[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ARRAY_BUFFER,[56C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 490 [m[38;2;238;238;238m[48;2;40;40;40m[27C(BATCH_MAX [38;2;244;55;83m*[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mInstanceData[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m()) [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244misize[m[38;2;238;238;238m[48;2;40;40;40m,[24C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 491 [m[38;2;238;238;238m[48;2;40;40;40m[27C[38;2;201;208;92mptr[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnull[m[38;2;238;238;238m[48;2;40;40;40m(), [38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::STREAM_DRAW);[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 492 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// coords[m[38;2;238;238;238m[48;2;40;40;40m[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 493 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribPointer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m2[m[38;2;238;238;238m[48;2;40;40;40m,[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 494 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FLOAT, [38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FALSE,[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 495 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mInstanceData[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m,[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 496 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mptr[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnull[m[38;2;238;238;238m[48;2;40;40;40m());[51C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 497 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mEnableVertexAttribArray[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m);[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 498 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribDivisor[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m);[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 499 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// glyphoffset[m[38;2;238;238;238m[48;2;40;40;40m[74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 500 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribPointer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m2[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m4[m[38;2;238;238;238m[48;2;40;40;40m,[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 501 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FLOAT, [38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FALSE,[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 502 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mInstanceData[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m,[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 503 [m[38;2;238;238;238m[48;2;40;40;40m[36C([38;2;255;194;75m2[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m*[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m()) [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m *[38;2;115;206;244mconst[m[38;2;238;238;238m[48;2;40;40;40m _);[28C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 504 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mEnableVertexAttribArray[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m2[m[38;2;238;238;238m[48;2;40;40;40m);[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 505 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribDivisor[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m2[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m);[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 506 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// uv[m[38;2;238;238;238m[48;2;40;40;40m[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 507 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribPointer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m3[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m4[m[38;2;238;238;238m[48;2;40;40;40m,[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 508 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FLOAT, [38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FALSE,[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 509 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mInstanceData[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m,[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 510 [m[38;2;238;238;238m[48;2;40;40;40m[36C([38;2;255;194;75m6[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m*[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m()) [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m *[38;2;115;206;244mconst[m[38;2;238;238;238m[48;2;40;40;40m _);[28C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 511 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mEnableVertexAttribArray[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m3[m[38;2;238;238;238m[48;2;40;40;40m);[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 512 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribDivisor[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m3[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m);[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 513 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// [m[38;2;238;238;238m[48;2;40;40;40m[1m[4m[38;2;255;255;255mcolor[m[38;2;238;238;238m[48;2;40;40;40m[80C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 514 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribPointer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m4[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m3[m[38;2;238;238;238m[48;2;40;40;40m,[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 515 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FLOAT, [38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FALSE,[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H515,37[8C35%[56;42H[?12l[?25h[?25l[1;56r[1;1H[28M[1;57r[29;1H[38;2;68;68;68m 516 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mInstanceData[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m,[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 517 [m[38;2;238;238;238m[48;2;40;40;40m[36C([38;2;255;194;75m10[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m*[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m()) [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m *[38;2;115;206;244mconst[m[38;2;238;238;238m[48;2;40;40;40m _);[27C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 518 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mEnableVertexAttribArray[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m4[m[38;2;238;238;238m[48;2;40;40;40m);[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 519 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribDivisor[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m4[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m);[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 520 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// [m[38;2;238;238;238m[48;2;40;40;40m[1m[4m[38;2;255;255;255mcolor[m[38;2;238;238;238m[48;2;40;40;40m[80C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 521 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribPointer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m5[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m3[m[38;2;238;238;238m[48;2;40;40;40m,[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 522 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FLOAT, [38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FALSE,[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 523 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mInstanceData[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m,[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 524 [m[38;2;238;238;238m[48;2;40;40;40m[36C([38;2;255;194;75m13[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m*[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m()) [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m *[38;2;115;206;244mconst[m[38;2;238;238;238m[48;2;40;40;40m _);[27C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 525 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mEnableVertexAttribArray[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m5[m[38;2;238;238;238m[48;2;40;40;40m);[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 526 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribDivisor[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m5[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m);[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 527 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 528 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindVertexArray[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m);[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 529 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindBuffer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ARRAY_BUFFER, [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m);[52C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 530 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 531 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 532 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m (msg_tx, msg_rx) [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mmpsc[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mchannel[m[38;2;238;238;238m[48;2;40;40;40m();[53C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 533 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 534 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mcfg![m[38;2;238;238;238m[48;2;40;40;40m(feature [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;211;185;135m"live-shader-reload"[m[38;2;238;238;238m[48;2;40;40;40m) {[51C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 535 [m[38;2;238;238;238m[48;2;40;40;40m[12C::[38;2;201;208;92mstd[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mthread[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mspawn[m[38;2;238;238;238m[48;2;40;40;40m([38;2;115;206;244mmove[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m||[m[38;2;238;238;238m[48;2;40;40;40m {[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 536 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m (tx, rx) [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m ::[38;2;201;208;92mstd[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92msync[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mmpsc[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mchannel[m[38;2;238;238;238m[48;2;40;40;40m();[40C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 537 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m watcher [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mWatcher[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m(tx).[38;2;179;222;239mexpect[m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"create file watcher"[m[38;2;238;238;238m[48;2;40;40;40m);[19C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 538 [m[38;2;238;238;238m[48;2;40;40;40m[16Cwatcher.[38;2;179;222;239mwatch[m[38;2;238;238;238m[48;2;40;40;40m(TEXT_SHADER_F_PATH).[38;2;179;222;239mexpect[m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"watch fragment shader"[m[38;2;238;238;238m[48;2;40;40;40m);[18C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 539 [m[38;2;238;238;238m[48;2;40;40;40m[16Cwatcher.[38;2;179;222;239mwatch[m[38;2;238;238;238m[48;2;40;40;40m(TEXT_SHADER_V_PATH).[38;2;179;222;239mexpect[m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"watch vertex shader"[m[38;2;238;238;238m[48;2;40;40;40m);[20C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 540 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 541 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;201;208;92mloop[m[38;2;238;238;238m[48;2;40;40;40m {[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 542 [m[38;2;238;238;238m[48;2;40;40;40m[20C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m event [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m rx.[38;2;179;222;239mrecv[m[38;2;238;238;238m[48;2;40;40;40m().[38;2;179;222;239mexpect[m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"watcher event"[m[38;2;238;238;238m[48;2;40;40;40m);[34C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 543 [m[38;2;238;238;238m[48;2;40;40;40m[20C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m ::[38;2;201;208;92mnotify[m[38;2;238;238;238m[48;2;40;40;40m::Event { path, op } [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m event;[39C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H543,21[8C37%[56;26H[?12l[?25h[?25l[1;56r[1;1H[28M[1;57r[29;1H[38;2;68;68;68m 544 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 545 [m[38;2;238;238;238m[48;2;40;40;40m[20C[38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75mOk[m[38;2;238;238;238m[48;2;40;40;40m(op) [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m op {[60C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 546 [m[38;2;238;238;238m[48;2;40;40;40m[24C[38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m op.[38;2;179;222;239mcontains[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mop[m[38;2;238;238;238m[48;2;40;40;40m::RENAME) {[48C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 547 [m[38;2;238;238;238m[48;2;40;40;40m[28C[38;2;179;222;239mcontinue[m[38;2;238;238;238m[48;2;40;40;40m;[63C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 548 [m[38;2;238;238;238m[48;2;40;40;40m[24C}[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 549 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 550 [m[38;2;238;238;238m[48;2;40;40;40m[24C[38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m op.[38;2;179;222;239mcontains[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mop[m[38;2;238;238;238m[48;2;40;40;40m::IGNORED) {[47C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 551 [m[38;2;238;238;238m[48;2;40;40;40m[28C[38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75mSome[m[38;2;238;238;238m[48;2;40;40;40m(path) [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m path.[38;2;179;222;239mas_ref[m[38;2;238;238;238m[48;2;40;40;40m() {[37C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 552 [m[38;2;238;238;238m[48;2;40;40;40m[32C[38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75mErr[m[38;2;238;238;238m[48;2;40;40;40m(err) [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m watcher.[38;2;179;222;239mwatch[m[38;2;238;238;238m[48;2;40;40;40m(path) {[29C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 553 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mwarn![m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"failed to establish watch on {:?}: {:?}"[m[38;2;238;238;238m[48;2;40;40;40m, path, err);    [48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 554 [m[38;2;238;238;238m[48;2;40;40;40m[32C}[67C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 555 [m[38;2;238;238;238m[48;2;40;40;40m[28C}[71C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 556 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 557 [m[38;2;238;238;238m[48;2;40;40;40m[28Cmsg_tx.[38;2;179;222;239msend[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mMsg[m[38;2;238;238;238m[48;2;40;40;40m::ShaderReload)[42C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 558 [m[38;2;238;238;238m[48;2;40;40;40m[32C.[38;2;179;222;239mexpect[m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"msg send ok"[m[38;2;238;238;238m[48;2;40;40;40m);[45C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 559 [m[38;2;238;238;238m[48;2;40;40;40m[24C}[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 560 [m[38;2;238;238;238m[48;2;40;40;40m[20C}[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 561 [m[38;2;238;238;238m[48;2;40;40;40m[16C}[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 562 [m[38;2;238;238;238m[48;2;40;40;40m[12C});[85C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 563 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 564 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 565 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m renderer [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m QuadRenderer {[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 566 [m[38;2;238;238;238m[48;2;40;40;40m[12Cprogram: program,[71C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 567 [m[38;2;238;238;238m[48;2;40;40;40m[12Cvao: vao,[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 568 [m[38;2;238;238;238m[48;2;40;40;40m[12Cvbo: vbo,[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 569 [m[38;2;238;238;238m[48;2;40;40;40m[12Cebo: ebo,[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 570 [m[38;2;238;238;238m[48;2;40;40;40m[12Cvbo_instance: vbo_instance,[61C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 571 [m[38;2;238;238;238m[48;2;40;40;40m[12Catlas: [38;2;115;206;244mVec[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m(),[70C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H571,13[8C39%[56;18H[?12l[?25h[?25l[1;56r[1;1H[28M[1;57r[29;1H[38;2;68;68;68m 572 [m[38;2;238;238;238m[48;2;40;40;40m[12Cactive_tex: [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m,[74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 573 [m[38;2;238;238;238m[48;2;40;40;40m[12Cbatch: [38;2;201;208;92mBatch[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m(),[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 574 [m[38;2;238;238;238m[48;2;40;40;40m[12Crx: msg_rx,[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 575 [m[38;2;238;238;238m[48;2;40;40;40m[8C};[90C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 576 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 577 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m atlas [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mAtlas[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m(ATLAS_SIZE);[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 578 [m[38;2;238;238;238m[48;2;40;40;40m[8Crenderer.atlas.[38;2;179;222;239mpush[m[38;2;238;238;238m[48;2;40;40;40m(atlas);[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 579 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 580 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;255;194;75mOk[m[38;2;238;238;238m[48;2;40;40;40m(renderer)[80C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 581 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 582 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 583 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mwith_api[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mF, T[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m([74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 584 [m[38;2;238;238;238m[48;2;40;40;40m[8C&[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self,[82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 585 [m[38;2;238;238;238m[48;2;40;40;40m[8Cconfig: &Config,[76C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 586 [m[38;2;238;238;238m[48;2;40;40;40m[8Cprops: &[38;2;201;208;92mterm[m[38;2;238;238;238m[48;2;40;40;40m::SizeInfo,[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 587 [m[38;2;238;238;238m[48;2;40;40;40m[8Cvisual_bell_intensity: [38;2;115;206;244mf64[m[38;2;238;238;238m[48;2;40;40;40m,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 588 [m[38;2;238;238;238m[48;2;40;40;40m[8Cfunc: F[85C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 589 [m[38;2;238;238;238m[48;2;40;40;40m    ) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m T[90C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 590 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mwhere[m[38;2;238;238;238m[48;2;40;40;40m F: [38;2;115;206;244mFnOnce[m[38;2;238;238;238m[48;2;40;40;40m(RenderApi) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m T[61C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 591 [m[38;2;238;238;238m[48;2;40;40;40m    {[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 592 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mwhile[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75mOk[m[38;2;238;238;238m[48;2;40;40;40m(msg) [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m self.rx.[38;2;179;222;239mtry_recv[m[38;2;238;238;238m[48;2;40;40;40m() {[52C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 593 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mmatch[m[38;2;238;238;238m[48;2;40;40;40m msg {[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 594 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;201;208;92mMsg[m[38;2;238;238;238m[48;2;40;40;40m::ShaderReload [38;2;244;55;83m=>[m[38;2;238;238;238m[48;2;40;40;40m [1m[38;2;244;55;83m{[m[38;2;238;238;238m[48;2;40;40;40m[62C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 595 [m[38;2;238;238;238m[48;2;40;40;40m[20Cself.[38;2;179;222;239mreload_shaders[m[38;2;238;238;238m[48;2;40;40;40m(&config, Size {[45C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 596 [m[38;2;238;238;238m[48;2;40;40;40m[24Cwidth: [38;2;179;222;239mPixels[m[38;2;238;238;238m[48;2;40;40;40m(props.width [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mu32[m[38;2;238;238;238m[48;2;40;40;40m),[42C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 597 [m[38;2;238;238;238m[48;2;40;40;40m[24Cheight: [38;2;179;222;239mPixels[m[38;2;238;238;238m[48;2;40;40;40m(props.height [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mu32[m[38;2;238;238;238m[48;2;40;40;40m)[41C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 598 [m[38;2;238;238;238m[48;2;40;40;40m[20C});[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 599 [m[38;2;238;238;238m[48;2;40;40;40m[16C[1m[38;2;244;55;83m}[m[38;2;238;238;238m[48;2;40;40;40m[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H599,17[8C41%[56;22H[?12l[?25h[?25l[1;56r[1;1H[28M[1;57r[23;43H{ [28;22H} 
[38;2;68;68;68m 600 [m[38;2;238;238;238m[48;2;40;40;40m[12C}[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 601 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 602 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 603 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239munsafe[m[38;2;238;238;238m[48;2;40;40;40m {[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 604 [m[38;2;238;238;238m[48;2;40;40;40m[12Cself.program.[38;2;179;222;239mactivate[m[38;2;238;238;238m[48;2;40;40;40m();[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 605 [m[38;2;238;238;238m[48;2;40;40;40m[12Cself.program.[38;2;179;222;239mset_term_uniforms[m[38;2;238;238;238m[48;2;40;40;40m(props);[50C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 606 [m[38;2;238;238;238m[48;2;40;40;40m[12Cself.program.[38;2;179;222;239mset_visual_bell[m[38;2;238;238;238m[48;2;40;40;40m(visual_bell_intensity [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m _);[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 607 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 608 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindVertexArray[m[38;2;238;238;238m[48;2;40;40;40m(self.vao);[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 609 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindBuffer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ELEMENT_ARRAY_BUFFER, self.ebo);[37C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 610 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindBuffer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ARRAY_BUFFER, self.vbo_instance);[36C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 611 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mActiveTexture[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::TEXTURE0);[56C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 612 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 613 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 614 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m res [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfunc[m[38;2;238;238;238m[48;2;40;40;40m(RenderApi {[66C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 615 [m[38;2;238;238;238m[48;2;40;40;40m[12Cactive_tex: &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self.active_tex,[55C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 616 [m[38;2;238;238;238m[48;2;40;40;40m[12Cbatch: &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self.batch,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 617 [m[38;2;238;238;238m[48;2;40;40;40m[12Catlas: &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self.atlas,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 618 [m[38;2;238;238;238m[48;2;40;40;40m[12Cprogram: &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self.program,[61C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 619 [m[38;2;238;238;238m[48;2;40;40;40m[12Cvisual_bell_intensity: visual_bell_intensity [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m _,[38C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 620 [m[38;2;238;238;238m[48;2;40;40;40m[12Cconfig: config,[73C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 621 [m[38;2;238;238;238m[48;2;40;40;40m[8C});[89C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 622 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 623 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239munsafe[m[38;2;238;238;238m[48;2;40;40;40m {[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 624 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindBuffer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ELEMENT_ARRAY_BUFFER, [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m);[44C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 625 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindBuffer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ARRAY_BUFFER, [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m);[52C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 626 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindVertexArray[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m);[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 627 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H627,0-1[7C43%[56;6H[?12l[?25h[?25l[1;56r[1;1H[28M[1;57r[29;1H[38;2;68;68;68m 628 [m[38;2;238;238;238m[48;2;40;40;40m[12Cself.program.[38;2;179;222;239mdeactivate[m[38;2;238;238;238m[48;2;40;40;40m();[62C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 629 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 630 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 631 [m[38;2;238;238;238m[48;2;40;40;40m[8Cres[89C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 632 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 633 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 634 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mwith_loader[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mF, T[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m(&[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self, func: F) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m T[47C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 635 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mwhere[m[38;2;238;238;238m[48;2;40;40;40m F: [38;2;115;206;244mFnOnce[m[38;2;238;238;238m[48;2;40;40;40m(LoaderApi) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m T[61C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 636 [m[38;2;238;238;238m[48;2;40;40;40m    {[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 637 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239munsafe[m[38;2;238;238;238m[48;2;40;40;40m {[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 638 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mActiveTexture[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::TEXTURE0);[56C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 639 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 640 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 641 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mfunc[m[38;2;238;238;238m[48;2;40;40;40m(LoaderApi {[76C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 642 [m[38;2;238;238;238m[48;2;40;40;40m[12Cactive_tex: &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self.active_tex,[55C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 643 [m[38;2;238;238;238m[48;2;40;40;40m[12Catlas: &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self.atlas,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 644 [m[38;2;238;238;238m[48;2;40;40;40m[8C})[90C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 645 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 646 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 647 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mreload_shaders[m[38;2;238;238;238m[48;2;40;40;40m(&[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self, config: &Config, size: Size[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mPixels[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mu32[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m>>[m[38;2;238;238;238m[48;2;40;40;40m) {[20C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 648 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92minfo![m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"Reloading shaders"[m[38;2;238;238;238m[48;2;40;40;40m);[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 649 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m program [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mmatch[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mShaderProgram[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m(config, size) {[38C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 650 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;255;194;75mOk[m[38;2;238;238;238m[48;2;40;40;40m(program) [38;2;244;55;83m=>[m[38;2;238;238;238m[48;2;40;40;40m program,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 651 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;255;194;75mErr[m[38;2;238;238;238m[48;2;40;40;40m(err) [38;2;244;55;83m=>[m[38;2;238;238;238m[48;2;40;40;40m {[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 652 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;201;208;92mmatch[m[38;2;238;238;238m[48;2;40;40;40m err {[73C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 653 [m[38;2;238;238;238m[48;2;40;40;40m[20C[38;2;201;208;92mShaderCreationError[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mIo[m[38;2;238;238;238m[48;2;40;40;40m(err) [38;2;244;55;83m=>[m[38;2;238;238;238m[48;2;40;40;40m [1m[38;2;244;55;83m{[m[38;2;238;238;238m[48;2;40;40;40m[47C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 654 [m[38;2;238;238;238m[48;2;40;40;40m[24C[38;2;201;208;92merror![m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"Error reading shader file: {}"[m[38;2;238;238;238m[48;2;40;40;40m, err);[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 655 [m[38;2;238;238;238m[48;2;40;40;40m[20C[1m[38;2;244;55;83m}[m[38;2;238;238;238m[48;2;40;40;40m,[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H655,21[8C46%[56;26H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m 572 [m[38;2;238;238;238m[48;2;40;40;40m[12Cactive_tex: [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m,[74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 573 [m[38;2;238;238;238m[48;2;40;40;40m[12Cbatch: [38;2;201;208;92mBatch[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m(),[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 574 [m[38;2;238;238;238m[48;2;40;40;40m[12Crx: msg_rx,[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 575 [m[38;2;238;238;238m[48;2;40;40;40m[8C};[90C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 576 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 577 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m atlas [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mAtlas[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m(ATLAS_SIZE);[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 578 [m[38;2;238;238;238m[48;2;40;40;40m[8Crenderer.atlas.[38;2;179;222;239mpush[m[38;2;238;238;238m[48;2;40;40;40m(atlas);[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 579 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 580 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;255;194;75mOk[m[38;2;238;238;238m[48;2;40;40;40m(renderer)[80C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 581 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 582 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 583 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mwith_api[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mF, T[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m([74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 584 [m[38;2;238;238;238m[48;2;40;40;40m[8C&[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self,[82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 585 [m[38;2;238;238;238m[48;2;40;40;40m[8Cconfig: &Config,[76C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 586 [m[38;2;238;238;238m[48;2;40;40;40m[8Cprops: &[38;2;201;208;92mterm[m[38;2;238;238;238m[48;2;40;40;40m::SizeInfo,[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 587 [m[38;2;238;238;238m[48;2;40;40;40m[8Cvisual_bell_intensity: [38;2;115;206;244mf64[m[38;2;238;238;238m[48;2;40;40;40m,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 588 [m[38;2;238;238;238m[48;2;40;40;40m[8Cfunc: F[85C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 589 [m[38;2;238;238;238m[48;2;40;40;40m    ) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m T[90C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 590 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mwhere[m[38;2;238;238;238m[48;2;40;40;40m F: [38;2;115;206;244mFnOnce[m[38;2;238;238;238m[48;2;40;40;40m(RenderApi) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m T[61C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 591 [m[38;2;238;238;238m[48;2;40;40;40m    {[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 592 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mwhile[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75mOk[m[38;2;238;238;238m[48;2;40;40;40m(msg) [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m self.rx.[38;2;179;222;239mtry_recv[m[38;2;238;238;238m[48;2;40;40;40m() {[52C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 593 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mmatch[m[38;2;238;238;238m[48;2;40;40;40m msg {[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 594 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;201;208;92mMsg[m[38;2;238;238;238m[48;2;40;40;40m::ShaderReload [38;2;244;55;83m=>[m[38;2;238;238;238m[48;2;40;40;40m {[62C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 595 [m[38;2;238;238;238m[48;2;40;40;40m[20Cself.[38;2;179;222;239mreload_shaders[m[38;2;238;238;238m[48;2;40;40;40m(&config, Size {[45C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 596 [m[38;2;238;238;238m[48;2;40;40;40m[24Cwidth: [38;2;179;222;239mPixels[m[38;2;238;238;238m[48;2;40;40;40m(props.width [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mu32[m[38;2;238;238;238m[48;2;40;40;40m),[42C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 597 [m[38;2;238;238;238m[48;2;40;40;40m[24Cheight: [38;2;179;222;239mPixels[m[38;2;238;238;238m[48;2;40;40;40m(props.height [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mu32[m[38;2;238;238;238m[48;2;40;40;40m)[41C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 598 [m[38;2;238;238;238m[48;2;40;40;40m[20C});[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 599 [m[38;2;238;238;238m[48;2;40;40;40m[16C}[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H627,0-1[7C43%[56;6H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m 544 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 545 [m[38;2;238;238;238m[48;2;40;40;40m[20C[38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75mOk[m[38;2;238;238;238m[48;2;40;40;40m(op) [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m op {[60C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 546 [m[38;2;238;238;238m[48;2;40;40;40m[24C[38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m op.[38;2;179;222;239mcontains[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mop[m[38;2;238;238;238m[48;2;40;40;40m::RENAME) {[48C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 547 [m[38;2;238;238;238m[48;2;40;40;40m[28C[38;2;179;222;239mcontinue[m[38;2;238;238;238m[48;2;40;40;40m;[63C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 548 [m[38;2;238;238;238m[48;2;40;40;40m[24C}[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 549 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 550 [m[38;2;238;238;238m[48;2;40;40;40m[24C[38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m op.[38;2;179;222;239mcontains[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mop[m[38;2;238;238;238m[48;2;40;40;40m::IGNORED) {[47C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 551 [m[38;2;238;238;238m[48;2;40;40;40m[28C[38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75mSome[m[38;2;238;238;238m[48;2;40;40;40m(path) [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m path.[38;2;179;222;239mas_ref[m[38;2;238;238;238m[48;2;40;40;40m() {[37C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 552 [m[38;2;238;238;238m[48;2;40;40;40m[32C[38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75mErr[m[38;2;238;238;238m[48;2;40;40;40m(err) [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m watcher.[38;2;179;222;239mwatch[m[38;2;238;238;238m[48;2;40;40;40m(path) {[29C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 553 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mwarn![m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"failed to establish watch on {:?}: {:?}"[m[38;2;238;238;238m[48;2;40;40;40m, path, err);    [48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 554 [m[38;2;238;238;238m[48;2;40;40;40m[32C}[67C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 555 [m[38;2;238;238;238m[48;2;40;40;40m[28C}[71C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 556 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 557 [m[38;2;238;238;238m[48;2;40;40;40m[28Cmsg_tx.[38;2;179;222;239msend[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mMsg[m[38;2;238;238;238m[48;2;40;40;40m::ShaderReload)[42C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 558 [m[38;2;238;238;238m[48;2;40;40;40m[32C.[38;2;179;222;239mexpect[m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"msg send ok"[m[38;2;238;238;238m[48;2;40;40;40m);[45C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 559 [m[38;2;238;238;238m[48;2;40;40;40m[24C}[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 560 [m[38;2;238;238;238m[48;2;40;40;40m[20C}[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 561 [m[38;2;238;238;238m[48;2;40;40;40m[16C}[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 562 [m[38;2;238;238;238m[48;2;40;40;40m[12C});[85C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 563 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 564 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 565 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m renderer [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m QuadRenderer {[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 566 [m[38;2;238;238;238m[48;2;40;40;40m[12Cprogram: program,[71C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 567 [m[38;2;238;238;238m[48;2;40;40;40m[12Cvao: vao,[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 568 [m[38;2;238;238;238m[48;2;40;40;40m[12Cvbo: vbo,[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 569 [m[38;2;238;238;238m[48;2;40;40;40m[12Cebo: ebo,[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 570 [m[38;2;238;238;238m[48;2;40;40;40m[12Cvbo_instance: vbo_instance,[61C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 571 [m[38;2;238;238;238m[48;2;40;40;40m[12Catlas: [38;2;115;206;244mVec[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m(),[70C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[51;43H[1m[38;2;244;55;83m{[56;22H}[m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H599,17[8C41%[56;22H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m 516 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mInstanceData[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m,[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 517 [m[38;2;238;238;238m[48;2;40;40;40m[36C([38;2;255;194;75m10[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m*[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m()) [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m *[38;2;115;206;244mconst[m[38;2;238;238;238m[48;2;40;40;40m _);[27C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 518 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mEnableVertexAttribArray[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m4[m[38;2;238;238;238m[48;2;40;40;40m);[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 519 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribDivisor[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m4[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m);[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 520 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// [m[38;2;238;238;238m[48;2;40;40;40m[1m[4m[38;2;255;255;255mcolor[m[38;2;238;238;238m[48;2;40;40;40m[80C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 521 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribPointer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m5[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m3[m[38;2;238;238;238m[48;2;40;40;40m,[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 522 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FLOAT, [38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FALSE,[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 523 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mInstanceData[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m,[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 524 [m[38;2;238;238;238m[48;2;40;40;40m[36C([38;2;255;194;75m13[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m*[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m()) [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m *[38;2;115;206;244mconst[m[38;2;238;238;238m[48;2;40;40;40m _);[27C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 525 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mEnableVertexAttribArray[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m5[m[38;2;238;238;238m[48;2;40;40;40m);[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 526 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribDivisor[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m5[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m);[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 527 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 528 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindVertexArray[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m);[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 529 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindBuffer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ARRAY_BUFFER, [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m);[52C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 530 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 531 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 532 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m (msg_tx, msg_rx) [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mmpsc[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mchannel[m[38;2;238;238;238m[48;2;40;40;40m();[53C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 533 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 534 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mcfg![m[38;2;238;238;238m[48;2;40;40;40m(feature [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;211;185;135m"live-shader-reload"[m[38;2;238;238;238m[48;2;40;40;40m) {[51C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 535 [m[38;2;238;238;238m[48;2;40;40;40m[12C::[38;2;201;208;92mstd[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mthread[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mspawn[m[38;2;238;238;238m[48;2;40;40;40m([38;2;115;206;244mmove[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m||[m[38;2;238;238;238m[48;2;40;40;40m {[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 536 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m (tx, rx) [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m ::[38;2;201;208;92mstd[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92msync[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mmpsc[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mchannel[m[38;2;238;238;238m[48;2;40;40;40m();[40C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 537 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m watcher [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mWatcher[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m(tx).[38;2;179;222;239mexpect[m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"create file watcher"[m[38;2;238;238;238m[48;2;40;40;40m);[19C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 538 [m[38;2;238;238;238m[48;2;40;40;40m[16Cwatcher.[38;2;179;222;239mwatch[m[38;2;238;238;238m[48;2;40;40;40m(TEXT_SHADER_F_PATH).[38;2;179;222;239mexpect[m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"watch fragment shader"[m[38;2;238;238;238m[48;2;40;40;40m);[18C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 539 [m[38;2;238;238;238m[48;2;40;40;40m[16Cwatcher.[38;2;179;222;239mwatch[m[38;2;238;238;238m[48;2;40;40;40m(TEXT_SHADER_V_PATH).[38;2;179;222;239mexpect[m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"watch vertex shader"[m[38;2;238;238;238m[48;2;40;40;40m);[20C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 540 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 541 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;201;208;92mloop[m[38;2;238;238;238m[48;2;40;40;40m {[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 542 [m[38;2;238;238;238m[48;2;40;40;40m[20C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m event [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m rx.[38;2;179;222;239mrecv[m[38;2;238;238;238m[48;2;40;40;40m().[38;2;179;222;239mexpect[m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"watcher event"[m[38;2;238;238;238m[48;2;40;40;40m);[34C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 543 [m[38;2;238;238;238m[48;2;40;40;40m[20C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m ::[38;2;201;208;92mnotify[m[38;2;238;238;238m[48;2;40;40;40m::Event { path, op } [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m event;[39C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H571,13[8C39%[56;18H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m 488 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindBuffer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ARRAY_BUFFER, vbo_instance);[41C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 489 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBufferData[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ARRAY_BUFFER,[56C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 490 [m[38;2;238;238;238m[48;2;40;40;40m[27C(BATCH_MAX [38;2;244;55;83m*[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mInstanceData[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m()) [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244misize[m[38;2;238;238;238m[48;2;40;40;40m,[24C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 491 [m[38;2;238;238;238m[48;2;40;40;40m[27C[38;2;201;208;92mptr[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnull[m[38;2;238;238;238m[48;2;40;40;40m(), [38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::STREAM_DRAW);[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 492 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// coords[m[38;2;238;238;238m[48;2;40;40;40m[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 493 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribPointer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m2[m[38;2;238;238;238m[48;2;40;40;40m,[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 494 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FLOAT, [38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FALSE,[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 495 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mInstanceData[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m,[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 496 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mptr[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnull[m[38;2;238;238;238m[48;2;40;40;40m());[51C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 497 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mEnableVertexAttribArray[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m);[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 498 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribDivisor[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m);[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 499 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// glyphoffset[m[38;2;238;238;238m[48;2;40;40;40m[74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 500 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribPointer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m2[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m4[m[38;2;238;238;238m[48;2;40;40;40m,[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 501 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FLOAT, [38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FALSE,[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 502 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mInstanceData[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m,[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 503 [m[38;2;238;238;238m[48;2;40;40;40m[36C([38;2;255;194;75m2[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m*[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m()) [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m *[38;2;115;206;244mconst[m[38;2;238;238;238m[48;2;40;40;40m _);[28C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 504 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mEnableVertexAttribArray[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m2[m[38;2;238;238;238m[48;2;40;40;40m);[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 505 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribDivisor[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m2[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m);[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 506 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// uv[m[38;2;238;238;238m[48;2;40;40;40m[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 507 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribPointer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m3[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m4[m[38;2;238;238;238m[48;2;40;40;40m,[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 508 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FLOAT, [38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FALSE,[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 509 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mInstanceData[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m,[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 510 [m[38;2;238;238;238m[48;2;40;40;40m[36C([38;2;255;194;75m6[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m*[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m()) [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m *[38;2;115;206;244mconst[m[38;2;238;238;238m[48;2;40;40;40m _);[28C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 511 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mEnableVertexAttribArray[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m3[m[38;2;238;238;238m[48;2;40;40;40m);[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 512 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribDivisor[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m3[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m);[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 513 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// [m[38;2;238;238;238m[48;2;40;40;40m[1m[4m[38;2;255;255;255mcolor[m[38;2;238;238;238m[48;2;40;40;40m[80C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 514 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribPointer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m4[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m3[m[38;2;238;238;238m[48;2;40;40;40m,[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 515 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FLOAT, [38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FALSE,[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H543,21[8C37%[56;26H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m 460 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindBuffer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ARRAY_BUFFER, vbo);[50C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 461 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 462 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mVertexAttribPointer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m2[m[38;2;238;238;238m[48;2;40;40;40m,[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 463 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FLOAT, [38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::FALSE,[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 464 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mPackedVertex[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m,[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 465 [m[38;2;238;238;238m[48;2;40;40;40m[36C[38;2;201;208;92mptr[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnull[m[38;2;238;238;238m[48;2;40;40;40m());[51C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 466 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mEnableVertexAttribArray[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m);[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 467 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 468 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBufferData[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ARRAY_BUFFER,[56C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 469 [m[38;2;238;238;238m[48;2;40;40;40m[27C([38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mPackedVertex[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83m*[m[38;2;238;238;238m[48;2;40;40;40m vertices.[38;2;179;222;239mlen[m[38;2;238;238;238m[48;2;40;40;40m()) [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m GLsizeiptr,[14C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 470 [m[38;2;238;238;238m[48;2;40;40;40m[27Cvertices.[38;2;179;222;239mas_ptr[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m *[38;2;115;206;244mconst[m[38;2;238;238;238m[48;2;40;40;40m _,[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 471 [m[38;2;238;238;238m[48;2;40;40;40m[27C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::STATIC_DRAW);[56C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 472 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 473 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// ---------------------[m[38;2;238;238;238m[48;2;40;40;40m[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 474 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// Set up element buffer[m[38;2;238;238;238m[48;2;40;40;40m[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 475 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// ---------------------[m[38;2;238;238;238m[48;2;40;40;40m[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 476 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m indices: [[38;2;115;206;244mu32[m[38;2;238;238;238m[48;2;40;40;40m; [38;2;255;194;75m6[m[38;2;238;238;238m[48;2;40;40;40m] [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [[38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m3[m[38;2;238;238;238m[48;2;40;40;40m,[55C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 477 [m[38;2;238;238;238m[48;2;40;40;40m[37C[38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m2[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m3[m[38;2;238;238;238m[48;2;40;40;40m];[54C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 478 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 479 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindBuffer[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ELEMENT_ARRAY_BUFFER, ebo);[42C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 480 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBufferData[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ELEMENT_ARRAY_BUFFER,[48C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 481 [m[38;2;238;238;238m[48;2;40;40;40m[27C([38;2;255;194;75m6[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m*[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mu32[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m()) [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244misize[m[38;2;238;238;238m[48;2;40;40;40m,[41C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 482 [m[38;2;238;238;238m[48;2;40;40;40m[27Cindices.[38;2;179;222;239mas_ptr[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m *[38;2;115;206;244mconst[m[38;2;238;238;238m[48;2;40;40;40m _,[44C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 483 [m[38;2;238;238;238m[48;2;40;40;40m[27C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::STATIC_DRAW);[56C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 484 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 485 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// ----------------------------[m[38;2;238;238;238m[48;2;40;40;40m[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 486 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// Setup vertex instance buffer[m[38;2;238;238;238m[48;2;40;40;40m[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 487 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// ----------------------------[m[38;2;238;238;238m[48;2;40;40;40m[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H515,37[8C35%[56;42H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m 432 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m vao: GLuint [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m;[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 433 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m vbo: GLuint [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m;[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 434 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m ebo: GLuint [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m;[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 435 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 436 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m vbo_instance: GLuint [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m;[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 437 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 438 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239munsafe[m[38;2;238;238;238m[48;2;40;40;40m {[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 439 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mEnable[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::BLEND);[66C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 440 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBlendFunc[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::SRC1_[1m[4m[38;2;255;255;255mCOLOR[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::ONE_MINUS_SRC1_[1m[4m[38;2;255;255;255mCOLOR[m[38;2;238;238;238m[48;2;40;40;40m);[32C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 441 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mEnable[m[38;2;238;238;238m[48;2;40;40;40m([38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::MULTISAMPLE);[60C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 442 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 443 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mGenVertexArrays[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m, &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m vao);[55C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 444 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mGenBuffers[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m, &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m vbo);[60C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 445 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mGenBuffers[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m, &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m ebo);[60C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 446 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mGenBuffers[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m1[m[38;2;238;238;238m[48;2;40;40;40m, &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m vbo_instance);[51C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 447 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mgl[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mBindVertexArray[m[38;2;238;238;238m[48;2;40;40;40m(vao);[63C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 448 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 449 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// ----------------------------[m[38;2;238;238;238m[48;2;40;40;40m[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 450 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// setup vertex position buffer[m[38;2;238;238;238m[48;2;40;40;40m[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 451 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// ----------------------------[m[38;2;238;238;238m[48;2;40;40;40m[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 452 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;102;102;102m// Top right, Bottom right, Bottom left, Top left[m[38;2;238;238;238m[48;2;40;40;40m[39C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 453 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m vertices [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [[72C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 454 [m[38;2;238;238;238m[48;2;40;40;40m[16CPackedVertex { x: [38;2;255;194;75m1.0[m[38;2;238;238;238m[48;2;40;40;40m, y: [38;2;255;194;75m1.0[m[38;2;238;238;238m[48;2;40;40;40m },[52C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 455 [m[38;2;238;238;238m[48;2;40;40;40m[16CPackedVertex { x: [38;2;255;194;75m1.0[m[38;2;238;238;238m[48;2;40;40;40m, y: [38;2;255;194;75m0.0[m[38;2;238;238;238m[48;2;40;40;40m },[52C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 456 [m[38;2;238;238;238m[48;2;40;40;40m[16CPackedVertex { x: [38;2;255;194;75m0.0[m[38;2;238;238;238m[48;2;40;40;40m, y: [38;2;255;194;75m0.0[m[38;2;238;238;238m[48;2;40;40;40m },[52C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 457 [m[38;2;238;238;238m[48;2;40;40;40m[16CPackedVertex { x: [38;2;255;194;75m0.0[m[38;2;238;238;238m[48;2;40;40;40m, y: [38;2;255;194;75m1.0[m[38;2;238;238;238m[48;2;40;40;40m },[52C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 458 [m[38;2;238;238;238m[48;2;40;40;40m[12C];[86C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 459 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H487,13[8C33%[56;18H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m 404 [m[38;2;238;238;238m[48;2;40;40;40m[8CBATCH_MAX[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 405 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 406 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 407 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;201;208;92m#[inline][m[38;2;238;238;238m[48;2;40;40;40m[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 408 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mis_empty[m[38;2;238;238;238m[48;2;40;40;40m(&self) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mbool[m[38;2;238;238;238m[48;2;40;40;40m {[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 409 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.[38;2;179;222;239mlen[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83m==[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 410 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 411 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 412 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;201;208;92m#[inline][m[38;2;238;238;238m[48;2;40;40;40m[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 413 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239msize[m[38;2;238;238;238m[48;2;40;40;40m(&self) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244musize[m[38;2;238;238;238m[48;2;40;40;40m {[67C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 414 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.[38;2;179;222;239mlen[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83m*[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239msize_of[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mInstanceData[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m()[54C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 415 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 416 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 417 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mclear[m[38;2;238;238;238m[48;2;40;40;40m(&[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self) {[71C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 418 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.tex [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m;[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 419 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.instances.[38;2;179;222;239mclear[m[38;2;238;238;238m[48;2;40;40;40m();[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 420 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 421 [m[38;2;238;238;238m[48;2;40;40;40m}[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 422 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 423 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m/// Maximum items to be drawn in a batch.[m[38;2;238;238;238m[48;2;40;40;40m[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 424 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mconst[m[38;2;238;238;238m[48;2;40;40;40m BATCH_MAX: [38;2;115;206;244musize[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75m65_536[m[38;2;238;238;238m[48;2;40;40;40m;[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 425 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mconst[m[38;2;238;238;238m[48;2;40;40;40m ATLAS_SIZE: [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75m1024[m[38;2;238;238;238m[48;2;40;40;40m;[71C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 426 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 427 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mimpl[m[38;2;238;238;238m[48;2;40;40;40m QuadRenderer {[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 428 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// [m[38;2;238;238;238m[48;2;40;40;40mTODO[38;2;102;102;102m should probably hand this a transform instead of width/height[m[38;2;238;238;238m[48;2;40;40;40m[27C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 429 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m(config: &Config, size: Size[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mPixels[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mu32[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m>>[m[38;2;238;238;238m[48;2;40;40;40m) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mResult[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mQuadRenderer, Error[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m {[11C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 430 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m program [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mShaderProgram[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m(config, size)[38;2;244;55;83m?[m[38;2;238;238;238m[48;2;40;40;40m;[44C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 431 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H459,0-1[7C31%[56;6H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m 376 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 377 [m[38;2;238;238;238m[48;2;40;40;40m[12Cuv_bot: glyph.uv_bot,[67C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 378 [m[38;2;238;238;238m[48;2;40;40;40m[12Cuv_left: glyph.uv_left,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 379 [m[38;2;238;238;238m[48;2;40;40;40m[12Cuv_width: glyph.uv_width,[63C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 380 [m[38;2;238;238;238m[48;2;40;40;40m[12Cuv_height: glyph.uv_height,[61C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 381 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 382 [m[38;2;238;238;238m[48;2;40;40;40m[12Cr: cell.fg.r [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 383 [m[38;2;238;238;238m[48;2;40;40;40m[12Cg: cell.fg.g [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 384 [m[38;2;238;238;238m[48;2;40;40;40m[12Cb: cell.fg.b [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 385 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 386 [m[38;2;238;238;238m[48;2;40;40;40m[12Cbg_r: cell.bg.r [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 387 [m[38;2;238;238;238m[48;2;40;40;40m[12Cbg_g: cell.bg.g [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 388 [m[38;2;238;238;238m[48;2;40;40;40m[12Cbg_b: cell.bg.b [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 389 [m[38;2;238;238;238m[48;2;40;40;40m[8C});[89C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 390 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 391 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 392 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;201;208;92m#[inline][m[38;2;238;238;238m[48;2;40;40;40m[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 393 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfull[m[38;2;238;238;238m[48;2;40;40;40m(&self) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mbool[m[38;2;238;238;238m[48;2;40;40;40m {[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 394 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.[38;2;179;222;239mcapacity[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83m==[m[38;2;238;238;238m[48;2;40;40;40m self.[38;2;179;222;239mlen[m[38;2;238;238;238m[48;2;40;40;40m()[63C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 395 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 396 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 397 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;201;208;92m#[inline][m[38;2;238;238;238m[48;2;40;40;40m[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 398 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mlen[m[38;2;238;238;238m[48;2;40;40;40m(&self) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244musize[m[38;2;238;238;238m[48;2;40;40;40m {[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 399 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.instances.[38;2;179;222;239mlen[m[38;2;238;238;238m[48;2;40;40;40m()[72C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 400 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 401 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 402 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;201;208;92m#[inline][m[38;2;238;238;238m[48;2;40;40;40m[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 403 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mcapacity[m[38;2;238;238;238m[48;2;40;40;40m(&self) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244musize[m[38;2;238;238;238m[48;2;40;40;40m {[63C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H431,0-1[7C28%[56;6H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m 348 [m[38;2;238;238;238m[48;2;40;40;40m}[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 349 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 350 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mimpl[m[38;2;238;238;238m[48;2;40;40;40m Batch {[88C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 351 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;201;208;92m#[inline][m[38;2;238;238;238m[48;2;40;40;40m[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 352 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m() [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m Batch {[73C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 353 [m[38;2;238;238;238m[48;2;40;40;40m[8CBatch {[85C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 354 [m[38;2;238;238;238m[48;2;40;40;40m[12Ctex: [38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m,[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 355 [m[38;2;238;238;238m[48;2;40;40;40m[12Cinstances: [38;2;115;206;244mVec[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mwith_capacity[m[38;2;238;238;238m[48;2;40;40;40m(BATCH_MAX),[47C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 356 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 357 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 358 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 359 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239madd_item[m[38;2;238;238;238m[48;2;40;40;40m([80C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 360 [m[38;2;238;238;238m[48;2;40;40;40m[8C&[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self,[82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 361 [m[38;2;238;238;238m[48;2;40;40;40m[8Ccell: &RenderableCell,[70C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 362 [m[38;2;238;238;238m[48;2;40;40;40m[8Cglyph: &Glyph,[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 363 [m[38;2;238;238;238m[48;2;40;40;40m    ) {[93C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 364 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m self.[38;2;179;222;239mis_empty[m[38;2;238;238;238m[48;2;40;40;40m() {[72C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 365 [m[38;2;238;238;238m[48;2;40;40;40m[12Cself.tex [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m glyph.tex_id;[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 366 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 367 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 368 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.instances.[38;2;179;222;239mpush[m[38;2;238;238;238m[48;2;40;40;40m(InstanceData {[58C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 369 [m[38;2;238;238;238m[48;2;40;40;40m[12Ccol: cell.column.[38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[62C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 370 [m[38;2;238;238;238m[48;2;40;40;40m[12Crow: cell.line.[38;2;255;194;75m0[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 371 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 372 [m[38;2;238;238;238m[48;2;40;40;40m[12Ctop: glyph.top,[73C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 373 [m[38;2;238;238;238m[48;2;40;40;40m[12Cleft: glyph.left,[71C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 374 [m[38;2;238;238;238m[48;2;40;40;40m[12Cwidth: glyph.width,[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 375 [m[38;2;238;238;238m[48;2;40;40;40m[12Cheight: glyph.height,[67C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H403,5[9C26%[56;10H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m 320 [m[38;2;238;238;238m[48;2;40;40;40m}[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 321 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 322 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[derive([m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mDebug[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m)][m[38;2;238;238;238m[48;2;40;40;40m[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 323 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mRenderApi[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m'a[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m {[74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 324 [m[38;2;238;238;238m[48;2;40;40;40m    active_tex: &'a [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m GLuint,[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 325 [m[38;2;238;238;238m[48;2;40;40;40m    batch: &'a [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m Batch,[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 326 [m[38;2;238;238;238m[48;2;40;40;40m    atlas: &'a [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mVec[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mAtlas[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m,[70C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 327 [m[38;2;238;238;238m[48;2;40;40;40m    program: &'a [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m ShaderProgram,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 328 [m[38;2;238;238;238m[48;2;40;40;40m    config: &'a Config,[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 329 [m[38;2;238;238;238m[48;2;40;40;40m    visual_bell_intensity: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m[70C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 330 [m[38;2;238;238;238m[48;2;40;40;40m}[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 331 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 332 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[derive([m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mDebug[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m)][m[38;2;238;238;238m[48;2;40;40;40m[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 333 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mLoaderApi[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m'a[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m {[74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 334 [m[38;2;238;238;238m[48;2;40;40;40m    active_tex: &'a [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m GLuint,[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 335 [m[38;2;238;238;238m[48;2;40;40;40m    atlas: &'a [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mVec[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mAtlas[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m,[70C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 336 [m[38;2;238;238;238m[48;2;40;40;40m}[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 337 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 338 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[derive([m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mDebug[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m)][m[38;2;238;238;238m[48;2;40;40;40m[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 339 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mPackedVertex[m[38;2;238;238;238m[48;2;40;40;40m {[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 340 [m[38;2;238;238;238m[48;2;40;40;40m    x: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[89C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 341 [m[38;2;238;238;238m[48;2;40;40;40m    y: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[89C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 342 [m[38;2;238;238;238m[48;2;40;40;40m}[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 343 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 344 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[derive([m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mDebug[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m)][m[38;2;238;238;238m[48;2;40;40;40m[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 345 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mBatch[m[38;2;238;238;238m[48;2;40;40;40m {[82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 346 [m[38;2;238;238;238m[48;2;40;40;40m    tex: GLuint,[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 347 [m[38;2;238;238;238m[48;2;40;40;40m    instances: [38;2;115;206;244mVec[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mInstanceData[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m,[67C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H375,13[8C24%[56;18H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m 292 [m[38;2;238;238;238m[48;2;40;40;40m    height: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 293 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// uv offset[m[38;2;238;238;238m[48;2;40;40;40m[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 294 [m[38;2;238;238;238m[48;2;40;40;40m    uv_left: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 295 [m[38;2;238;238;238m[48;2;40;40;40m    uv_bot: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 296 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// uv scale[m[38;2;238;238;238m[48;2;40;40;40m[85C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 297 [m[38;2;238;238;238m[48;2;40;40;40m    uv_width: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 298 [m[38;2;238;238;238m[48;2;40;40;40m    uv_height: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 299 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// [m[38;2;238;238;238m[48;2;40;40;40m[1m[4m[38;2;255;255;255mcolor[m[38;2;238;238;238m[48;2;40;40;40m[88C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 300 [m[38;2;238;238;238m[48;2;40;40;40m    r: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[89C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 301 [m[38;2;238;238;238m[48;2;40;40;40m    g: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[89C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 302 [m[38;2;238;238;238m[48;2;40;40;40m    b: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[89C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 303 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// background [m[38;2;238;238;238m[48;2;40;40;40m[1m[4m[38;2;255;255;255mcolor[m[38;2;238;238;238m[48;2;40;40;40m[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 304 [m[38;2;238;238;238m[48;2;40;40;40m    bg_r: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[86C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 305 [m[38;2;238;238;238m[48;2;40;40;40m    bg_g: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[86C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 306 [m[38;2;238;238;238m[48;2;40;40;40m    bg_b: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[86C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 307 [m[38;2;238;238;238m[48;2;40;40;40m}[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 308 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 309 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[derive([m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mDebug[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m)][m[38;2;238;238;238m[48;2;40;40;40m[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 310 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mQuadRenderer[m[38;2;238;238;238m[48;2;40;40;40m {[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 311 [m[38;2;238;238;238m[48;2;40;40;40m    program: ShaderProgram,[73C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 312 [m[38;2;238;238;238m[48;2;40;40;40m    vao: GLuint,[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 313 [m[38;2;238;238;238m[48;2;40;40;40m    vbo: GLuint,[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 314 [m[38;2;238;238;238m[48;2;40;40;40m    ebo: GLuint,[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 315 [m[38;2;238;238;238m[48;2;40;40;40m    vbo_instance: GLuint,[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 316 [m[38;2;238;238;238m[48;2;40;40;40m    atlas: [38;2;115;206;244mVec[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mAtlas[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m,[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 317 [m[38;2;238;238;238m[48;2;40;40;40m    active_tex: GLuint,[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 318 [m[38;2;238;238;238m[48;2;40;40;40m    batch: Batch,[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 319 [m[38;2;238;238;238m[48;2;40;40;40m    rx: [38;2;201;208;92mmpsc[m[38;2;238;238;238m[48;2;40;40;40m::Receiver[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mMsg[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m,[72C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H347,5[9C22%[56;10H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m 264 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m rasterizer [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self.rasterizer;[54C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 265 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m metrics [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m &self.metrics;[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 266 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.cache[82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 267 [m[38;2;238;238;238m[48;2;40;40;40m[12C.[38;2;179;222;239mentry[m[38;2;238;238;238m[48;2;40;40;40m(*glyph_key)[70C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 268 [m[38;2;238;238;238m[48;2;40;40;40m[12C.[38;2;179;222;239mor_insert_with[m[38;2;238;238;238m[48;2;40;40;40m([38;2;244;55;83m||[m[38;2;238;238;238m[48;2;40;40;40m {[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 269 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m rasterized [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m rasterizer.[38;2;179;222;239mget_glyph[m[38;2;238;238;238m[48;2;40;40;40m(&glyph_key)[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 270 [m[38;2;238;238;238m[48;2;40;40;40m[20C.[38;2;179;222;239munwrap_or_else[m[38;2;238;238;238m[48;2;40;40;40m([38;2;244;55;83m|[m[38;2;238;238;238m[48;2;40;40;40m_[38;2;244;55;83m|[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mDefault[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mdefault[m[38;2;238;238;238m[48;2;40;40;40m());[40C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 271 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 272 [m[38;2;238;238;238m[48;2;40;40;40m[16Crasterized.left [38;2;244;55;83m+=[m[38;2;238;238;238m[48;2;40;40;40m glyph_offset.x [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m;[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 273 [m[38;2;238;238;238m[48;2;40;40;40m[16Crasterized.top [38;2;244;55;83m+=[m[38;2;238;238;238m[48;2;40;40;40m glyph_offset.y [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m;[44C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 274 [m[38;2;238;238;238m[48;2;40;40;40m[16Crasterized.top [38;2;244;55;83m-=[m[38;2;238;238;238m[48;2;40;40;40m metrics.descent [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mi32[m[38;2;238;238;238m[48;2;40;40;40m;[43C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 275 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 276 [m[38;2;238;238;238m[48;2;40;40;40m[16Cloader.[38;2;179;222;239mload_glyph[m[38;2;238;238;238m[48;2;40;40;40m(&rasterized)[54C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 277 [m[38;2;238;238;238m[48;2;40;40;40m[12C})[86C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 278 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 279 [m[38;2;238;238;238m[48;2;40;40;40m}[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 280 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 281 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[derive([m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mDebug[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m)][m[38;2;238;238;238m[48;2;40;40;40m[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 282 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[repr(C)][m[38;2;238;238;238m[48;2;40;40;40m[90C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 283 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mInstanceData[m[38;2;238;238;238m[48;2;40;40;40m {[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 284 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// coords[m[38;2;238;238;238m[48;2;40;40;40m[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 285 [m[38;2;238;238;238m[48;2;40;40;40m    col: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 286 [m[38;2;238;238;238m[48;2;40;40;40m    row: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 287 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// glyph offset[m[38;2;238;238;238m[48;2;40;40;40m[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 288 [m[38;2;238;238;238m[48;2;40;40;40m    left: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[86C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 289 [m[38;2;238;238;238m[48;2;40;40;40m    top: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 290 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// glyph scale[m[38;2;238;238;238m[48;2;40;40;40m[82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 291 [m[38;2;238;238;238m[48;2;40;40;40m    width: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[85C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H319,5[9C20%[56;10H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m 236 [m[38;2;238;238;238m[48;2;40;40;40m[12C([38;2;201;208;92m$font[m[38;2;238;238;238m[48;2;40;40;40m:expr) [38;2;244;55;83m=>[m[38;2;238;238;238m[48;2;40;40;40m {[71C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 237 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;201;208;92mfor[m[38;2;238;238;238m[48;2;40;40;40m i [38;2;179;222;239min[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mRangeInclusive[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m([38;2;255;194;75m32u8[m[38;2;238;238;238m[48;2;40;40;40m, [38;2;255;194;75m128u8[m[38;2;238;238;238m[48;2;40;40;40m) {[41C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 238 [m[38;2;238;238;238m[48;2;40;40;40m[20Ccache.[38;2;179;222;239mget[m[38;2;238;238;238m[48;2;40;40;40m(&GlyphKey {[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 239 [m[38;2;238;238;238m[48;2;40;40;40m[24Cfont_key: [38;2;201;208;92m$font[m[38;2;238;238;238m[48;2;40;40;40m,[60C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 240 [m[38;2;238;238;238m[48;2;40;40;40m[24Cc: i [38;2;244;55;83mas[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mchar[m[38;2;238;238;238m[48;2;40;40;40m,[63C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 241 [m[38;2;238;238;238m[48;2;40;40;40m[24Csize: font.[38;2;179;222;239msize[m[38;2;238;238;238m[48;2;40;40;40m()[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 242 [m[38;2;238;238;238m[48;2;40;40;40m[20C}, loader);[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 243 [m[38;2;238;238;238m[48;2;40;40;40m[16C}[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 244 [m[38;2;238;238;238m[48;2;40;40;40m[12C}[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 245 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 246 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 247 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mload_glyphs_for_font![m[38;2;238;238;238m[48;2;40;40;40m(regular);[61C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 248 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mload_glyphs_for_font![m[38;2;238;238;238m[48;2;40;40;40m(bold);[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 249 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mload_glyphs_for_font![m[38;2;238;238;238m[48;2;40;40;40m(italic);[62C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 250 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 251 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;255;194;75mOk[m[38;2;238;238;238m[48;2;40;40;40m(cache)[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 252 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 253 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 254 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfont_metrics[m[38;2;238;238;238m[48;2;40;40;40m(&self) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::Metrics {[51C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 255 [m[38;2;238;238;238m[48;2;40;40;40m[8Cself.rasterizer[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 256 [m[38;2;238;238;238m[48;2;40;40;40m[12C.[38;2;179;222;239mmetrics[m[38;2;238;238;238m[48;2;40;40;40m(self.font_key)[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 257 [m[38;2;238;238;238m[48;2;40;40;40m[12C.[38;2;179;222;239mexpect[m[38;2;238;238;238m[48;2;40;40;40m([38;2;211;185;135m"metrics load since font is loaded at glyph cache creation"[m[38;2;238;238;238m[48;2;40;40;40m)[20C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 258 [m[38;2;238;238;238m[48;2;40;40;40m    }[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 259 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 260 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mget[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40m'a, L[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m(&'a [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m self, glyph_key: &GlyphKey, loader: &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m L) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m &'a Glyph[14C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 261 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mwhere[m[38;2;238;238;238m[48;2;40;40;40m L: LoadGlyph[74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 262 [m[38;2;238;238;238m[48;2;40;40;40m    {[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 263 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m glyph_offset [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m self.glyph_offset;[55C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H291,5[9C18%[56;10H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m 208 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;102;102;102m// Load bold font[m[38;2;238;238;238m[48;2;40;40;40m[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 209 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m bold_desc [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mmake_desc[m[38;2;238;238;238m[48;2;40;40;40m(&font.bold, [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mSlant[m[38;2;238;238;238m[48;2;40;40;40m::Normal, [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mWeight[m[38;2;238;238;238m[48;2;40;40;40m::Bold);[13C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 210 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 211 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m bold [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mload_or_regular[m[38;2;238;238;238m[48;2;40;40;40m(bold_desc, &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m rasterizer);[37C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 212 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 213 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;102;102;102m// Load italic font[m[38;2;238;238;238m[48;2;40;40;40m[73C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 214 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m italic_desc [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mmake_desc[m[38;2;238;238;238m[48;2;40;40;40m(&font.italic, [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mSlant[m[38;2;238;238;238m[48;2;40;40;40m::Italic, [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mWeight[m[38;2;238;238;238m[48;2;40;40;40m::Normal);[7C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 215 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 216 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m italic [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mload_or_regular[m[38;2;238;238;238m[48;2;40;40;40m(italic_desc, &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m rasterizer);[33C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 217 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 218 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;102;102;102m// Need to load at least one glyph for the face before calling metrics.[m[38;2;238;238;238m[48;2;40;40;40m[21C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 219 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;102;102;102m// The glyph requested here ('m' at the time of writing) has no special[m[38;2;238;238;238m[48;2;40;40;40m[21C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 220 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;102;102;102m// meaning.[m[38;2;238;238;238m[48;2;40;40;40m[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 221 [m[38;2;238;238;238m[48;2;40;40;40m[8Crasterizer.[38;2;179;222;239mget_glyph[m[38;2;238;238;238m[48;2;40;40;40m(&GlyphKey { font_key: regular, c: [38;2;255;194;75m'm'[m[38;2;238;238;238m[48;2;40;40;40m, size: font.[38;2;179;222;239msize[m[38;2;238;238;238m[48;2;40;40;40m() })[38;2;244;55;83m?[m[38;2;238;238;238m[48;2;40;40;40m;[10C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 222 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m metrics [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m rasterizer.[38;2;179;222;239mmetrics[m[38;2;238;238;238m[48;2;40;40;40m(regular)[38;2;244;55;83m?[m[38;2;238;238;238m[48;2;40;40;40m;[49C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 223 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 224 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m cache [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m GlyphCache {[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 225 [m[38;2;238;238;238m[48;2;40;40;40m[12Ccache: [38;2;201;208;92mHashMap[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mdefault[m[38;2;238;238;238m[48;2;40;40;40m(),[62C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 226 [m[38;2;238;238;238m[48;2;40;40;40m[12Crasterizer: rasterizer,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 227 [m[38;2;238;238;238m[48;2;40;40;40m[12Cfont_size: font.[38;2;179;222;239msize[m[38;2;238;238;238m[48;2;40;40;40m(),[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 228 [m[38;2;238;238;238m[48;2;40;40;40m[12Cfont_key: regular,[70C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 229 [m[38;2;238;238;238m[48;2;40;40;40m[12Cbold_key: bold,[73C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 230 [m[38;2;238;238;238m[48;2;40;40;40m[12Citalic_key: italic,[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 231 [m[38;2;238;238;238m[48;2;40;40;40m[12Cglyph_offset: glyph_offset,[61C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 232 [m[38;2;238;238;238m[48;2;40;40;40m[12Cmetrics: metrics[72C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 233 [m[38;2;238;238;238m[48;2;40;40;40m[8C};[90C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 234 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 235 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;201;208;92mmacro_rules![m[38;2;238;238;238m[48;2;40;40;40m load_glyphs_for_font {[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H263,9[9C15%[56;14H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m 180 [m[38;2;238;238;238m[48;2;40;40;40m[12Cdesc: &[38;2;201;208;92mconfig[m[38;2;238;238;238m[48;2;40;40;40m::FontDescription,[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 181 [m[38;2;238;238;238m[48;2;40;40;40m[12Cslant: [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::Slant,[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 182 [m[38;2;238;238;238m[48;2;40;40;40m[12Cweight: [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::Weight,[67C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 183 [m[38;2;238;238;238m[48;2;40;40;40m[8C) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m FontDesc[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 184 [m[38;2;238;238;238m[48;2;40;40;40m[8C{[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 185 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m style [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m [38;2;255;194;75mSome[m[38;2;238;238;238m[48;2;40;40;40m([38;2;115;206;244mref[m[38;2;238;238;238m[48;2;40;40;40m spec) [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m desc.style {[40C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 186 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mStyle[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mSpecific[m[38;2;238;238;238m[48;2;40;40;40m(spec.[38;2;179;222;239mto_owned[m[38;2;238;238;238m[48;2;40;40;40m())[46C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 187 [m[38;2;238;238;238m[48;2;40;40;40m[12C} [38;2;201;208;92melse[m[38;2;238;238;238m[48;2;40;40;40m {[80C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 188 [m[38;2;238;238;238m[48;2;40;40;40m[16C[38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mStyle[m[38;2;238;238;238m[48;2;40;40;40m::Description {slant:slant, weight:weight}[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 189 [m[38;2;238;238;238m[48;2;40;40;40m[12C};[86C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 190 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mFontDesc[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m(&desc.family[..], style)[50C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 191 [m[38;2;238;238;238m[48;2;40;40;40m[8C}[91C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 192 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 193 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;102;102;102m// Load regular font[m[38;2;238;238;238m[48;2;40;40;40m[72C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 194 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m regular_desc [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mmake_desc[m[38;2;238;238;238m[48;2;40;40;40m(&font.normal, [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mSlant[m[38;2;238;238;238m[48;2;40;40;40m::Normal, [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::[38;2;201;208;92mWeight[m[38;2;238;238;238m[48;2;40;40;40m::Normal);      [48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 195 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 196 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m regular [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m rasterizer[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 197 [m[38;2;238;238;238m[48;2;40;40;40m[12C.[38;2;179;222;239mload_font[m[38;2;238;238;238m[48;2;40;40;40m(&regular_desc, size)[38;2;244;55;83m?[m[38;2;238;238;238m[48;2;40;40;40m;[55C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 198 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 199 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;102;102;102m// helper to load a description if it is not the regular_desc[m[38;2;238;238;238m[48;2;40;40;40m[31C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 200 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m load_or_regular [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m [38;2;244;55;83m|[m[38;2;238;238;238m[48;2;40;40;40mdesc:FontDesc, rasterizer: &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m Rasterizer[38;2;244;55;83m|[m[38;2;238;238;238m[48;2;40;40;40m {[24C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 201 [m[38;2;238;238;238m[48;2;40;40;40m[12C[38;2;201;208;92mif[m[38;2;238;238;238m[48;2;40;40;40m desc [38;2;244;55;83m==[m[38;2;238;238;238m[48;2;40;40;40m regular_desc {[63C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 202 [m[38;2;238;238;238m[48;2;40;40;40m[16Cregular[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 203 [m[38;2;238;238;238m[48;2;40;40;40m[12C} [38;2;201;208;92melse[m[38;2;238;238;238m[48;2;40;40;40m {[80C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 204 [m[38;2;238;238;238m[48;2;40;40;40m[16Crasterizer.[38;2;179;222;239mload_font[m[38;2;238;238;238m[48;2;40;40;40m(&desc, size).[38;2;179;222;239munwrap_or_else[m[38;2;238;238;238m[48;2;40;40;40m([38;2;244;55;83m|[m[38;2;238;238;238m[48;2;40;40;40m_[38;2;244;55;83m|[m[38;2;238;238;238m[48;2;40;40;40m regular)[23C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 205 [m[38;2;238;238;238m[48;2;40;40;40m[12C}[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 206 [m[38;2;238;238;238m[48;2;40;40;40m[8C};[90C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 207 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H235,9[9C13%[56;14H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m 152 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// italic font[m[38;2;238;238;238m[48;2;40;40;40m[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 153 [m[38;2;238;238;238m[48;2;40;40;40m    italic_key: FontKey,[76C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 154 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 155 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// bold font[m[38;2;238;238;238m[48;2;40;40;40m[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 156 [m[38;2;238;238;238m[48;2;40;40;40m    bold_key: FontKey,[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 157 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 158 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// font size[m[38;2;238;238;238m[48;2;40;40;40m[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 159 [m[38;2;238;238;238m[48;2;40;40;40m    font_size: [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::Size,[74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 160 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 161 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// glyph offset[m[38;2;238;238;238m[48;2;40;40;40m[80C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 162 [m[38;2;238;238;238m[48;2;40;40;40m    glyph_offset: Delta,[76C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 163 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 164 [m[38;2;238;238;238m[48;2;40;40;40m    metrics: ::[38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::Metrics,[71C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 165 [m[38;2;238;238;238m[48;2;40;40;40m}[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 166 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 167 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mimpl[m[38;2;238;238;238m[48;2;40;40;40m GlyphCache {[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 168 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mnew[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mL[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m([82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 169 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m rasterizer: Rasterizer,[65C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 170 [m[38;2;238;238;238m[48;2;40;40;40m[8Cconfig: &Config,[76C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 171 [m[38;2;238;238;238m[48;2;40;40;40m[8Cloader: &[38;2;115;206;244mmut[m[38;2;238;238;238m[48;2;40;40;40m L[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 172 [m[38;2;238;238;238m[48;2;40;40;40m    ) [38;2;244;55;83m->[m[38;2;238;238;238m[48;2;40;40;40m [38;2;115;206;244mResult[m[38;2;238;238;238m[48;2;40;40;40m[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mGlyphCache, [38;2;201;208;92mfont[m[38;2;238;238;238m[48;2;40;40;40m::Error[38;2;244;55;83m>[m[38;2;238;238;238m[48;2;40;40;40m[60C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 173 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mwhere[m[38;2;238;238;238m[48;2;40;40;40m L: LoadGlyph[74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 174 [m[38;2;238;238;238m[48;2;40;40;40m    {[95C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 175 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m font [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m config.[38;2;179;222;239mfont[m[38;2;238;238;238m[48;2;40;40;40m();[67C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 176 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m size [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m font.[38;2;179;222;239msize[m[38;2;238;238;238m[48;2;40;40;40m();[69C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 177 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mlet[m[38;2;238;238;238m[48;2;40;40;40m glyph_offset [38;2;244;55;83m=[m[38;2;238;238;238m[48;2;40;40;40m *font.[38;2;179;222;239mglyph_offset[m[38;2;238;238;238m[48;2;40;40;40m();[52C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 178 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 179 [m[38;2;238;238;238m[48;2;40;40;40m[8C[38;2;179;222;239mfn[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mmake_desc[m[38;2;238;238;238m[48;2;40;40;40m([79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H207,0-1[7C11%[56;6H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m 124 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 125 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[derive([m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mDebug[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m, [m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mClone[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m)][m[38;2;238;238;238m[48;2;40;40;40m[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 126 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mGlyph[m[38;2;238;238;238m[48;2;40;40;40m {[82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 127 [m[38;2;238;238;238m[48;2;40;40;40m    tex_id: GLuint,[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 128 [m[38;2;238;238;238m[48;2;40;40;40m    top: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[87C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 129 [m[38;2;238;238;238m[48;2;40;40;40m    left: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[86C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 130 [m[38;2;238;238;238m[48;2;40;40;40m    width: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[85C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 131 [m[38;2;238;238;238m[48;2;40;40;40m    height: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 132 [m[38;2;238;238;238m[48;2;40;40;40m    uv_bot: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 133 [m[38;2;238;238;238m[48;2;40;40;40m    uv_left: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 134 [m[38;2;238;238;238m[48;2;40;40;40m    uv_width: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[82C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 135 [m[38;2;238;238;238m[48;2;40;40;40m    uv_height: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 136 [m[38;2;238;238;238m[48;2;40;40;40m}[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 137 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 138 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m/// Naïve glyph cache[m[38;2;238;238;238m[48;2;40;40;40m[79C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 139 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m///[m[38;2;238;238;238m[48;2;40;40;40m[97C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 140 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m/// Currently only keyed by `char`, and thus not possible to hold different[m[38;2;238;238;238m[48;2;40;40;40m[25C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 141 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m/// representations of the same code point.[m[38;2;238;238;238m[48;2;40;40;40m[57C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 142 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mGlyphCache[m[38;2;238;238;238m[48;2;40;40;40m {[77C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 143 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// Cache of buffered glyphs[m[38;2;238;238;238m[48;2;40;40;40m[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 144 [m[38;2;238;238;238m[48;2;40;40;40m    cache: HashMap[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mGlyphKey, Glyph, BuildHasherDefault[38;2;244;55;83m<[m[38;2;238;238;238m[48;2;40;40;40mFnvHasher[38;2;244;55;83m>>[m[38;2;238;238;238m[48;2;40;40;40m,[33C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 145 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 146 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// Rasterizer for loading new glyphs[m[38;2;238;238;238m[48;2;40;40;40m[59C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 147 [m[38;2;238;238;238m[48;2;40;40;40m    rasterizer: Rasterizer,[73C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 148 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 149 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// regular font[m[38;2;238;238;238m[48;2;40;40;40m[80C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 150 [m[38;2;238;238;238m[48;2;40;40;40m    font_key: FontKey,[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 151 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H179,9[10C9%[56;14H[?12l[?25h[?25l[1;56r[1;1H[28L[1;57r[1;1H[38;2;68;68;68m  96 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m///[m[38;2;238;238;238m[48;2;40;40;40m[97C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  97 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;102;102;102m/// Uniforms are prefixed with "u", and vertex attributes are prefixed with "a".[m[38;2;238;238;238m[48;2;40;40;40m[20C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  98 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m#[derive([m[38;2;238;238;238m[48;2;40;40;40m[38;2;115;206;244mDebug[m[38;2;238;238;238m[48;2;40;40;40m[38;2;201;208;92m)][m[38;2;238;238;238m[48;2;40;40;40m[84C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m  99 [m[38;2;238;238;238m[48;2;40;40;40m[38;2;179;222;239mpub[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mstruct[m[38;2;238;238;238m[48;2;40;40;40m [38;2;179;222;239mShaderProgram[m[38;2;238;238;238m[48;2;40;40;40m {[74C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 100 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m// Program id[m[38;2;238;238;238m[48;2;40;40;40m[83C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 101 [m[38;2;238;238;238m[48;2;40;40;40m    id: GLuint,[85C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 102 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 103 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// projection matrix uniform[m[38;2;238;238;238m[48;2;40;40;40m[67C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 104 [m[38;2;238;238;238m[48;2;40;40;40m    u_projection: GLint,[76C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 105 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 106 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// Terminal dimensions (pixels)[m[38;2;238;238;238m[48;2;40;40;40m[64C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 107 [m[38;2;238;238;238m[48;2;40;40;40m    u_term_dim: GLint,[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 108 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 109 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// Cell dimensions (pixels)[m[38;2;238;238;238m[48;2;40;40;40m[68C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 110 [m[38;2;238;238;238m[48;2;40;40;40m    u_cell_dim: GLint,[78C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 111 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 112 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// Visual bell[m[38;2;238;238;238m[48;2;40;40;40m[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 113 [m[38;2;238;238;238m[48;2;40;40;40m    u_visual_bell: GLint,[75C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 114 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 115 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// Background pass flag[m[38;2;238;238;238m[48;2;40;40;40m[72C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 116 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m///[m[38;2;238;238;238m[48;2;40;40;40m[93C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 117 [m[38;2;238;238;238m[48;2;40;40;40m    [38;2;102;102;102m/// Rendering is split into two passes; 1 for backgrounds, and one for text[m[38;2;238;238;238m[48;2;40;40;40m[21C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 118 [m[38;2;238;238;238m[48;2;40;40;40m    u_background: GLint,[76C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 119 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 120 [m[38;2;238;238;238m[48;2;40;40;40m    padding_x: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 121 [m[38;2;238;238;238m[48;2;40;40;40m    padding_y: [38;2;115;206;244mf32[m[38;2;238;238;238m[48;2;40;40;40m,[81C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 122 [m[38;2;238;238;238m[48;2;40;40;40m}[99C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m
[38;2;68;68;68m 123 [m[38;2;238;238;238m[48;2;40;40;40m[100C[48;2;50;50;50m [m[38;2;238;238;238m[48;2;40;40;40m[57;103H[K[57;103H151,0-1[8C7%[56;6H[?12l[?25h[?25l[57;103H[K[57;1H:[?12l[?25hq[?25l[?12l[?25h[?25l[57;2H[K[57;2H[?12l[?25h [?25l[101C151,0-1[8C7%[56;6H[?12l[?25h