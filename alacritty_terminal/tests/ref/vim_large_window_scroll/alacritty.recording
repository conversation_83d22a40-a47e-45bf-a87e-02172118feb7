[1m[7m%[27m[1m[0m                                                                                                                       
 

[0m[27m[24m[<EMAIL> [01;32m➜ [01;32m [36m~/code/alacritty [00m [K[?1h=[?2004h[1m[31mv[0m[39m[0m[35mv[35mi[39m[35mv[35mi[35mm[39m [36ms[39m[36ms[36mr[39m[36mr[36mc[39m[36mc[36m/[39m[36m/[36mr[39m[36mr[36me[39m[36me[36mn[39m[36mn[36mderer/[0m[39m[36m/[36mm[39m[36mm[36mo[39m[36mo[36md.rs[39m[1m [0m[0m [?1l>[?2004l

[?1049h[?1h=[2;1H▽[6n[2;1H  [1;1H[1;57r[?12;25h[?12l[?25h[27m[23m[m[38;2;234;234;234m[48;2;0;0;0m[H[2J[?25l[57;1H"src/renderer/mod.rs" 1354L, 40527C[>c[1;1H[38;2;66;66;66m  78 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mimpl[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m::std::fmt::[m[38;2;234;234;234m[48;2;0;0;0mDisplay [38;2;207;171;224mfor[m[38;2;234;234;234m[48;2;0;0;0m Error {[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  79 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mfmt[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, f: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m::std::fmt::[m[38;2;234;234;234m[48;2;0;0;0mFormatter) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m::std::fmt::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mResult[m[38;2;234;234;234m[48;2;0;0;0m {[28C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  80 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mmatch[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m {[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  81 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mError::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mShaderCreation[m[38;2;234;234;234m[48;2;0;0;0m([38;2;207;171;224mref[m[38;2;234;234;234m[48;2;0;0;0m err) [38;2;129;202;191m=>[m[38;2;234;234;234m[48;2;0;0;0m {[53C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  82 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;223;101;102mwrite![m[38;2;234;234;234m[48;2;0;0;0m(f, [38;2;197;209;92m"There was an error initializing the shaders: {}"[m[38;2;234;234;234m[48;2;0;0;0m, err)[19C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  83 [m[38;2;234;234;234m[48;2;0;0;0m[12C}[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  84 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  85 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  86 [m[38;2;234;234;234m[48;2;0;0;0m}[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  87 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  88 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mimpl[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mFrom[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mShaderCreationError[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfor[m[38;2;234;234;234m[48;2;0;0;0m Error {[58C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  89 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mfrom[m[38;2;234;234;234m[48;2;0;0;0m(val: ShaderCreationError) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m Error {[52C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  90 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;237;158;86mError::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mShaderCreation[m[38;2;234;234;234m[48;2;0;0;0m(val)[66C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  91 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  92 [m[38;2;234;234;234m[48;2;0;0;0m}[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  93 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  94 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  95 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m/// Text drawing program[m[38;2;234;234;234m[48;2;0;0;0m[76C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  96 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m///[m[38;2;234;234;234m[48;2;0;0;0m[97C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  97 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m/// Uniforms are prefixed with "u", and vertex attributes are prefixed with "a".[m[38;2;234;234;234m[48;2;0;0;0m[20C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  98 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m#[derive([m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mDebug[m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m)][m[38;2;234;234;234m[48;2;0;0;0m[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  99 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstruct[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mShaderProgram[m[38;2;234;234;234m[48;2;0;0;0m {[74C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 100 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m// Program id[m[38;2;234;234;234m[48;2;0;0;0m[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 101 [m[38;2;234;234;234m[48;2;0;0;0m    id: GLuint,[85C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 102 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 103 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// projection matrix uniform[m[38;2;234;234;234m[48;2;0;0;0m[67C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 104 [m[38;2;234;234;234m[48;2;0;0;0m    u_projection: GLint,[76C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 105 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 106 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// Terminal dimensions (pixels)[m[38;2;234;234;234m[48;2;0;0;0m[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 107 [m[38;2;234;234;234m[48;2;0;0;0m    u_term_dim: GLint,[78C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 108 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 109 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// Cell dimensions (pixels)[m[38;2;234;234;234m[48;2;0;0;0m[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 110 [m[38;2;234;234;234m[48;2;0;0;0m    u_cell_dim: GLint,[78C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 111 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 112 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// Visual bell[m[38;2;234;234;234m[48;2;0;0;0m[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 113 [m[38;2;234;234;234m[48;2;0;0;0m    u_visual_bell: GLint,[75C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 114 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 115 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// Background pass flag[m[38;2;234;234;234m[48;2;0;0;0m[72C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 116 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m///[m[38;2;234;234;234m[48;2;0;0;0m[93C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 117 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// Rendering is split into two passes; 1 for backgrounds, and one for text[m[38;2;234;234;234m[48;2;0;0;0m[21C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 118 [m[38;2;234;234;234m[48;2;0;0;0m    u_background: GLint,[76C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 119 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 120 [m[38;2;234;234;234m[48;2;0;0;0m    padding_x: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 121 [m[38;2;234;234;234m[48;2;0;0;0m    padding_y: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 122 [m[38;2;234;234;234m[48;2;0;0;0m}[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 123 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 124 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 125 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m#[derive([m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mDebug[m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m, [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mClone[m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m)][m[38;2;234;234;234m[48;2;0;0;0m[77C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 126 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstruct[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mGlyph[m[38;2;234;234;234m[48;2;0;0;0m {[82C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 127 [m[38;2;234;234;234m[48;2;0;0;0m    tex_id: GLuint,[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 128 [m[38;2;234;234;234m[48;2;0;0;0m    top: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 129 [m[38;2;234;234;234m[48;2;0;0;0m    left: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[86C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 130 [m[38;2;234;234;234m[48;2;0;0;0m    width: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[85C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 131 [m[38;2;234;234;234m[48;2;0;0;0m    height: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 132 [m[38;2;234;234;234m[48;2;0;0;0m    uv_bot: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m 133 [m[38;2;234;234;234m[48;2;0;0;0m    uv_left: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H105,0-1[8C5%[28;6H[?12l[?25h[?25l[27m[23m[m[38;2;234;234;234m[48;2;0;0;0m[H[2J[1;1H[38;2;66;66;66m   1 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// Copyright 2016 Joe Wilm, The Alacritty Project Contributors[m[38;2;234;234;234m[48;2;0;0;0m[38C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m   2 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m//[m[38;2;234;234;234m[48;2;0;0;0m[98C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m   3 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// Licensed under the Apache License, Version 2.0 (the "License");[m[38;2;234;234;234m[48;2;0;0;0m[34C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m   4 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// you may not use this file except in compliance with the License.[m[38;2;234;234;234m[48;2;0;0;0m[33C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m   5 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// You may obtain a copy of the License at[m[38;2;234;234;234m[48;2;0;0;0m[58C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m   6 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m//[m[38;2;234;234;234m[48;2;0;0;0m[98C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m   7 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m//     http://www.apache.org/licenses/LICENSE-2.0[m[38;2;234;234;234m[48;2;0;0;0m[51C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m   8 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m//[m[38;2;234;234;234m[48;2;0;0;0m[98C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m   9 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// Unless required by applicable law or agreed to in writing, software[m[38;2;234;234;234m[48;2;0;0;0m[30C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  10 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// distributed under the License is distributed on an "AS IS" BASIS,[m[38;2;234;234;234m[48;2;0;0;0m[32C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  11 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.[m[38;2;234;234;234m[48;2;0;0;0m[25C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  12 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// See the License for the specific language governing permissions and[m[38;2;234;234;234m[48;2;0;0;0m[30C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  13 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// limitations under the License.[m[38;2;234;234;234m[48;2;0;0;0m[67C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  14 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mstd::collections::[m[38;2;234;234;234m[48;2;0;0;0mHashMap;[70C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  15 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mstd::hash::[m[38;2;234;234;234m[48;2;0;0;0mBuildHasherDefault;[66C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  16 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mstd::fs::[m[38;2;234;234;234m[48;2;0;0;0mFile;[82C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  17 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mstd::io::[m[38;2;234;234;234m[48;2;0;0;0m{[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, Read};[74C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  18 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mstd::mem::[m[38;2;234;234;234m[48;2;0;0;0msize_of;[78C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  19 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mstd::path::[m[38;2;234;234;234m[48;2;0;0;0m{PathBuf};[75C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  20 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mstd::[m[38;2;234;234;234m[48;2;0;0;0mptr;[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  21 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mstd::sync::[m[38;2;234;234;234m[48;2;0;0;0mmpsc;[80C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  22 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  23 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m cgmath;[89C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  24 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mfnv::[m[38;2;234;234;234m[48;2;0;0;0mFnvHasher;[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  25 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mfont::[m[38;2;234;234;234m[48;2;0;0;0m{[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, Rasterizer, Rasterize, RasterizedGlyph, FontDesc, GlyphKey, FontKey};[14C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  26 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mgl::types::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m;[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  27 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m gl;[93C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  28 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mindex::[m[38;2;234;234;234m[48;2;0;0;0m{Line, Column, RangeInclusive};[58C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  29 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mnotify::[m[38;2;234;234;234m[48;2;0;0;0m{Watcher [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m WatcherApi, RecommendedWatcher [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m Watcher, op};[29C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  30 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  31 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mconfig::[m[38;2;234;234;234m[48;2;0;0;0m{[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, Config, Delta};[66C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  32 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mterm::[m[38;2;234;234;234m[48;2;0;0;0m{[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, cell, RenderableCell};[61C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  33 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mwindow::[m[38;2;234;234;234m[48;2;0;0;0m{Size, Pixels};[73C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  34 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  35 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224muse[m[38;2;234;234;234m[48;2;0;0;0m Rgb;[92C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  36 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  37 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// Shader paths for live reload[m[38;2;234;234;234m[48;2;0;0;0m[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  38 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mstatic[m[38;2;234;234;234m[48;2;0;0;0m TEXT_SHADER_F_PATH: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'static[23m[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstr[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;223;101;102mconcat![m[38;2;234;234;234m[48;2;0;0;0m([38;2;223;101;102menv![m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"CARGO_MANIFEST_DIR"[m[38;2;234;234;234m[48;2;0;0;0m), [38;2;197;209;92m"/res/text.f.glsl"[m[38;2;234;234;234m[48;2;0;0;0m);  [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  39 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mstatic[m[38;2;234;234;234m[48;2;0;0;0m TEXT_SHADER_V_PATH: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'static[23m[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstr[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;223;101;102mconcat![m[38;2;234;234;234m[48;2;0;0;0m([38;2;223;101;102menv![m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"CARGO_MANIFEST_DIR"[m[38;2;234;234;234m[48;2;0;0;0m), [38;2;197;209;92m"/res/text.v.glsl"[m[38;2;234;234;234m[48;2;0;0;0m);  [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  40 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  41 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m// Shader source which is used when live-shader-reload feature is disable[m[38;2;234;234;234m[48;2;0;0;0m[27C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  42 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mstatic[m[38;2;234;234;234m[48;2;0;0;0m TEXT_SHADER_F: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'static[23m[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstr[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;223;101;102minclude_str![m[38;2;234;234;234m[48;2;0;0;0m([50C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  43 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;223;101;102mconcat![m[38;2;234;234;234m[48;2;0;0;0m([38;2;223;101;102menv![m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"CARGO_MANIFEST_DIR"[m[38;2;234;234;234m[48;2;0;0;0m), [38;2;197;209;92m"/res/text.f.glsl"[m[38;2;234;234;234m[48;2;0;0;0m)[41C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  44 [m[38;2;234;234;234m[48;2;0;0;0m);[98C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  45 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mstatic[m[38;2;234;234;234m[48;2;0;0;0m TEXT_SHADER_V: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'static[23m[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstr[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;223;101;102minclude_str![m[38;2;234;234;234m[48;2;0;0;0m([50C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  46 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;223;101;102mconcat![m[38;2;234;234;234m[48;2;0;0;0m([38;2;223;101;102menv![m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"CARGO_MANIFEST_DIR"[m[38;2;234;234;234m[48;2;0;0;0m), [38;2;197;209;92m"/res/text.v.glsl"[m[38;2;234;234;234m[48;2;0;0;0m)[41C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  47 [m[38;2;234;234;234m[48;2;0;0;0m);[98C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  48 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  49 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m/// `LoadGlyph` allows for copying a rasterized glyph into graphics memory[m[38;2;234;234;234m[48;2;0;0;0m[26C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  50 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mtrait[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mLoadGlyph[m[38;2;234;234;234m[48;2;0;0;0m {[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  51 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// Load the rasterized glyph into GPU memory[m[38;2;234;234;234m[48;2;0;0;0m[51C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  52 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mload_glyph[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, rasterized: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mRasterizedGlyph) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m Glyph;[32C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  53 [m[38;2;234;234;234m[48;2;0;0;0m}[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  54 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  55 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224menum[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mMsg[m[38;2;234;234;234m[48;2;0;0;0m {[90C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m
[38;2;66;66;66m  56 [m[38;2;234;234;234m[48;2;0;0;0m    ShaderReload,[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H1,1[11CTop[1;6H[?12l[?25h[?25l[57;103H2[2;6H[?12l[?25h[?25l[57;103H3[3;6H[?12l[?25h[?25l[57;103H4[4;6H[?12l[?25h[?25l[57;103H5[5;6H[?12l[?25h[?25l[57;103H6[6;6H[?12l[?25h[?25l[57;103H7[7;6H[?12l[?25h[?25l[57;103H8[8;6H[?12l[?25h[?25l[57;103H9[9;6H[?12l[?25h[?25l[57;103H10,1[10;6H[?12l[?25h[?25l[57;104H1[11;6H[?12l[?25h[?25l[57;104H2[12;6H[?12l[?25h[?25l[57;104H3[13;6H[?12l[?25h[?25l[57;104H4[14;6H[?12l[?25h[?25l[57;104H5[15;6H[?12l[?25h[?25l[57;104H6[16;6H[?12l[?25h[?25l[57;104H7[17;6H[?12l[?25h[?25l[57;104H8[18;6H[?12l[?25h[?25l[57;104H9[19;6H[?12l[?25h[?25l[57;103H20[20;6H[?12l[?25h[?25l[57;104H1[21;6H[?12l[?25h[?25l[57;104H2,0-1[22;6H[?12l[?25h[?25l[57;104H3,1  [23;6H[?12l[?25h[?25l[57;104H4[24;6H[?12l[?25h[?25l[57;104H5[25;6H[?12l[?25h[?25l[57;104H6[26;6H[?12l[?25h[?25l[57;104H7[27;6H[?12l[?25h[?25l[57;104H8[28;6H[?12l[?25h[?25l[57;104H9[29;6H[?12l[?25h[?25l[57;103H30,0-1[30;6H[?12l[?25h[?25l[57;104H1,1  [31;6H[?12l[?25h[?25l[57;104H2[32;6H[?12l[?25h[?25l[57;104H3[33;6H[?12l[?25h[?25l[57;104H4,0-1[34;6H[?12l[?25h[?25l[57;104H5,1  [35;6H[?12l[?25h[?25l[57;104H6,0-1[36;6H[?12l[?25h[?25l[57;104H7,1  [37;6H[?12l[?25h[?25l[57;104H8[38;6H[?12l[?25h[?25l[57;104H9[39;6H[?12l[?25h[?25l[57;103H40,0-1[40;6H[?12l[?25h[?25l[57;104H1,1  [41;6H[?12l[?25h[?25l[57;104H2[42;6H[?12l[?25h[?25l[57;104H3[43;6H[?12l[?25h[?25l[42;55H[48;2;66;66;66m([44;6H)[m[38;2;234;234;234m[48;2;0;0;0m[57;104H4[44;6H[?12l[?25h[?25l[42;55H([44;6H)[57;104H5[45;6H[?12l[?25h[?25l[57;104H6[46;6H[?12l[?25h[?25l[45;55H[48;2;66;66;66m([47;6H)[m[38;2;234;234;234m[48;2;0;0;0m[57;104H7[47;6H[?12l[?25h[?25l[45;55H([47;6H)[57;104H8,0-1[48;6H[?12l[?25h[?25l[57;104H9,1  [49;6H[?12l[?25h[?25l[57;103H50[50;6H[?12l[?25h[?25l[57;104H1[51;6H[?12l[?25h[?25l[57;104H2[52;6H[?12l[?25h[?25l[50;26H[48;2;66;66;66m{[53;6H}[m[38;2;234;234;234m[48;2;0;0;0m[57;104H3[53;6H[?12l[?25h[?25l[50;26H{[53;6H}[57;104H4,0-1[54;6H[?12l[?25h[?25l[57;104H5,1  [55;6H[?12l[?25h[?25l[57;104H6[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[54;15H[48;2;66;66;66m{[m[38;2;234;234;234m[48;2;0;0;0m

[38;2;66;66;66m  57 [m[38;2;234;234;234m[48;2;0;0;0m[48;2;66;66;66m}[m[38;2;234;234;234m[48;2;0;0;0m[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H57,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[53;15H{[55;6H}
[38;2;66;66;66m  58 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H58,0-1[9C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  59 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m#[derive([m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mDebug[m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m)][m[38;2;234;234;234m[48;2;0;0;0m[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H59,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  60 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224menum[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mError[m[38;2;234;234;234m[48;2;0;0;0m {[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H60,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  61 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;140;182;225mShaderCreation[m[38;2;234;234;234m[48;2;0;0;0m(ShaderCreationError),[60C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H61,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[54;21H[48;2;66;66;66m{[m[38;2;234;234;234m[48;2;0;0;0m

[38;2;66;66;66m  62 [m[38;2;234;234;234m[48;2;0;0;0m[48;2;66;66;66m}[m[38;2;234;234;234m[48;2;0;0;0m[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H62,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[53;21H{[55;6H}
[38;2;66;66;66m  63 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H63,0-1[9C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  64 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mimpl[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m::std::error::[m[38;2;234;234;234m[48;2;0;0;0mError [38;2;207;171;224mfor[m[38;2;234;234;234m[48;2;0;0;0m Error {[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H64,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  65 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mcause[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mOption[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::std::error::[m[38;2;234;234;234m[48;2;0;0;0mError[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m {[47C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H65,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  66 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mmatch[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m {[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H66,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  67 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mError::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mShaderCreation[m[38;2;234;234;234m[48;2;0;0;0m([38;2;207;171;224mref[m[38;2;234;234;234m[48;2;0;0;0m err) [38;2;129;202;191m=>[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mSome[m[38;2;234;234;234m[48;2;0;0;0m(err),[44C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H67,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  68 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H68,1[11C0%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  69 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H69,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  70 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H70,0-1[9C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  71 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mdescription[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mstr[m[38;2;234;234;234m[48;2;0;0;0m {[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H71,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  72 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mmatch[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m {[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H72,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  73 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mError::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mShaderCreation[m[38;2;234;234;234m[48;2;0;0;0m([38;2;207;171;224mref[m[38;2;234;234;234m[48;2;0;0;0m err) [38;2;129;202;191m=>[m[38;2;234;234;234m[48;2;0;0;0m err.[38;2;140;182;225mdescription[m[38;2;234;234;234m[48;2;0;0;0m(),[36C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H73,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  74 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H74,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  75 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H75,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[44;41H[48;2;66;66;66m{[m[38;2;234;234;234m[48;2;0;0;0m[56;1H[38;2;66;66;66m  76 [m[38;2;234;234;234m[48;2;0;0;0m[48;2;66;66;66m}[m[38;2;234;234;234m[48;2;0;0;0m[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H76,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[43;41H{[55;6H}
[38;2;66;66;66m  77 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H77,0-1[9C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  78 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mimpl[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m::std::fmt::[m[38;2;234;234;234m[48;2;0;0;0mDisplay [38;2;207;171;224mfor[m[38;2;234;234;234m[48;2;0;0;0m Error {[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H78,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  79 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mfmt[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, f: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m::std::fmt::[m[38;2;234;234;234m[48;2;0;0;0mFormatter) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m::std::fmt::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mResult[m[38;2;234;234;234m[48;2;0;0;0m {[28C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H79,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  80 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mmatch[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m {[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H80,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  81 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mError::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mShaderCreation[m[38;2;234;234;234m[48;2;0;0;0m([38;2;207;171;224mref[m[38;2;234;234;234m[48;2;0;0;0m err) [38;2;129;202;191m=>[m[38;2;234;234;234m[48;2;0;0;0m {[53C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H81,1[11C1%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  82 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;223;101;102mwrite![m[38;2;234;234;234m[48;2;0;0;0m(f, [38;2;197;209;92m"There was an error initializing the shaders: {}"[m[38;2;234;234;234m[48;2;0;0;0m, err)[19C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H82,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  83 [m[38;2;234;234;234m[48;2;0;0;0m[12C}[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H83,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  84 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H84,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  85 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H85,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[48;41H[48;2;66;66;66m{[m[38;2;234;234;234m[48;2;0;0;0m[56;1H[38;2;66;66;66m  86 [m[38;2;234;234;234m[48;2;0;0;0m[48;2;66;66;66m}[m[38;2;234;234;234m[48;2;0;0;0m[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H86,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[47;41H{[55;6H}
[38;2;66;66;66m  87 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H87,0-1[9C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  88 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mimpl[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mFrom[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mShaderCreationError[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfor[m[38;2;234;234;234m[48;2;0;0;0m Error {[58C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H88,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  89 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mfrom[m[38;2;234;234;234m[48;2;0;0;0m(val: ShaderCreationError) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m Error {[52C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H89,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  90 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;237;158;86mError::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mShaderCreation[m[38;2;234;234;234m[48;2;0;0;0m(val)[66C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H90,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  91 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H91,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[52;47H[48;2;66;66;66m{[m[38;2;234;234;234m[48;2;0;0;0m[56;1H[38;2;66;66;66m  92 [m[38;2;234;234;234m[48;2;0;0;0m[48;2;66;66;66m}[m[38;2;234;234;234m[48;2;0;0;0m[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H92,1[11C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[51;47H{[55;6H}
[38;2;66;66;66m  93 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H93,0-1[9C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  94 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H94,0-1[9C2%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  95 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m/// Text drawing program[m[38;2;234;234;234m[48;2;0;0;0m[76C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H95,1[11C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  96 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m///[m[38;2;234;234;234m[48;2;0;0;0m[97C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H96,1[11C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  97 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m/// Uniforms are prefixed with "u", and vertex attributes are prefixed with "a".[m[38;2;234;234;234m[48;2;0;0;0m[20C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H97,1[11C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  98 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m#[derive([m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mDebug[m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m)][m[38;2;234;234;234m[48;2;0;0;0m[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H98,1[11C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m  99 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstruct[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mShaderProgram[m[38;2;234;234;234m[48;2;0;0;0m {[74C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H99,1[11C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 100 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m// Program id[m[38;2;234;234;234m[48;2;0;0;0m[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H100,1[10C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 101 [m[38;2;234;234;234m[48;2;0;0;0m    id: GLuint,[85C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H101,1[10C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 102 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H102,0-1[8C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 103 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// projection matrix uniform[m[38;2;234;234;234m[48;2;0;0;0m[67C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H103,1[10C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 104 [m[38;2;234;234;234m[48;2;0;0;0m    u_projection: GLint,[76C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H104,1[10C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 105 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H105,0-1[8C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 106 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// Terminal dimensions (pixels)[m[38;2;234;234;234m[48;2;0;0;0m[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H106,1[10C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 107 [m[38;2;234;234;234m[48;2;0;0;0m    u_term_dim: GLint,[78C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H107,1[10C3%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 108 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H108,0-1[8C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 109 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// Cell dimensions (pixels)[m[38;2;234;234;234m[48;2;0;0;0m[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H109,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 110 [m[38;2;234;234;234m[48;2;0;0;0m    u_cell_dim: GLint,[78C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H110,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 111 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H111,0-1[8C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 112 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// Visual bell[m[38;2;234;234;234m[48;2;0;0;0m[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H112,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 113 [m[38;2;234;234;234m[48;2;0;0;0m    u_visual_bell: GLint,[75C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H113,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 114 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H114,0-1[8C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 115 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// Background pass flag[m[38;2;234;234;234m[48;2;0;0;0m[72C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H115,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 116 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m///[m[38;2;234;234;234m[48;2;0;0;0m[93C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H116,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 117 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// Rendering is split into two passes; 1 for backgrounds, and one for text[m[38;2;234;234;234m[48;2;0;0;0m[21C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H117,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 118 [m[38;2;234;234;234m[48;2;0;0;0m    u_background: GLint,[76C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H118,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 119 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H119,0-1[8C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 120 [m[38;2;234;234;234m[48;2;0;0;0m    padding_x: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H120,1[10C4%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 121 [m[38;2;234;234;234m[48;2;0;0;0m    padding_y: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H121,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[33;31H[48;2;66;66;66m{[m[38;2;234;234;234m[48;2;0;0;0m[56;1H[38;2;66;66;66m 122 [m[38;2;234;234;234m[48;2;0;0;0m[48;2;66;66;66m}[m[38;2;234;234;234m[48;2;0;0;0m[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H122,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[32;31H{[55;6H}
[38;2;66;66;66m 123 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H123,0-1[8C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 124 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H124,0-1[8C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 125 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m#[derive([m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mDebug[m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m, [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mClone[m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m)][m[38;2;234;234;234m[48;2;0;0;0m[77C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H125,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 126 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstruct[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mGlyph[m[38;2;234;234;234m[48;2;0;0;0m {[82C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H126,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 127 [m[38;2;234;234;234m[48;2;0;0;0m    tex_id: GLuint,[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H127,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 128 [m[38;2;234;234;234m[48;2;0;0;0m    top: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H128,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 129 [m[38;2;234;234;234m[48;2;0;0;0m    left: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[86C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H129,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 130 [m[38;2;234;234;234m[48;2;0;0;0m    width: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[85C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H130,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 131 [m[38;2;234;234;234m[48;2;0;0;0m    height: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H131,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 132 [m[38;2;234;234;234m[48;2;0;0;0m    uv_bot: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H132,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 133 [m[38;2;234;234;234m[48;2;0;0;0m    uv_left: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H133,1[10C5%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 134 [m[38;2;234;234;234m[48;2;0;0;0m    uv_width: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[82C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H134,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 135 [m[38;2;234;234;234m[48;2;0;0;0m    uv_height: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H135,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[46;23H[48;2;66;66;66m{[m[38;2;234;234;234m[48;2;0;0;0m[56;1H[38;2;66;66;66m 136 [m[38;2;234;234;234m[48;2;0;0;0m[48;2;66;66;66m}[m[38;2;234;234;234m[48;2;0;0;0m[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H136,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[45;23H{[55;6H}
[38;2;66;66;66m 137 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H137,0-1[8C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 138 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m/// Naïve glyph cache[m[38;2;234;234;234m[48;2;0;0;0m[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H138,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 139 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m///[m[38;2;234;234;234m[48;2;0;0;0m[97C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H139,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 140 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m/// Currently only keyed by `char`, and thus not possible to hold different[m[38;2;234;234;234m[48;2;0;0;0m[25C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H140,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 141 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m/// representations of the same code point.[m[38;2;234;234;234m[48;2;0;0;0m[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H141,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 142 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstruct[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mGlyphCache[m[38;2;234;234;234m[48;2;0;0;0m {[77C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H142,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 143 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// Cache of buffered glyphs[m[38;2;234;234;234m[48;2;0;0;0m[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H143,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 144 [m[38;2;234;234;234m[48;2;0;0;0m    cache: HashMap[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mGlyphKey, Glyph, BuildHasherDefault[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mFnvHasher[38;2;129;202;191m>>[m[38;2;234;234;234m[48;2;0;0;0m,[33C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H144,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 145 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H145,0-1[8C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 146 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// Rasterizer for loading new glyphs[m[38;2;234;234;234m[48;2;0;0;0m[59C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H146,1[10C6%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 147 [m[38;2;234;234;234m[48;2;0;0;0m    rasterizer: Rasterizer,[73C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H147,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 148 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H148,0-1[8C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 149 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// regular font[m[38;2;234;234;234m[48;2;0;0;0m[80C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H149,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 150 [m[38;2;234;234;234m[48;2;0;0;0m    font_key: FontKey,[78C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H150,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 151 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H151,0-1[8C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 152 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// italic font[m[38;2;234;234;234m[48;2;0;0;0m[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H152,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 153 [m[38;2;234;234;234m[48;2;0;0;0m    italic_key: FontKey,[76C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H153,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 154 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H154,0-1[8C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 155 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// bold font[m[38;2;234;234;234m[48;2;0;0;0m[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H155,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 156 [m[38;2;234;234;234m[48;2;0;0;0m    bold_key: FontKey,[78C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H156,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 157 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H157,0-1[8C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 158 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// font size[m[38;2;234;234;234m[48;2;0;0;0m[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H158,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 159 [m[38;2;234;234;234m[48;2;0;0;0m    font_size: [38;2;237;158;86mfont::[m[38;2;234;234;234m[48;2;0;0;0mSize,[74C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H159,1[10C7%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 160 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H160,0-1[8C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 161 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// glyph offset[m[38;2;234;234;234m[48;2;0;0;0m[80C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H161,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 162 [m[38;2;234;234;234m[48;2;0;0;0m    glyph_offset: Delta,[76C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H162,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 163 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H163,0-1[8C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 164 [m[38;2;234;234;234m[48;2;0;0;0m    metrics: [38;2;237;158;86m::font::[m[38;2;234;234;234m[48;2;0;0;0mMetrics,[71C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H164,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[33;28H[48;2;66;66;66m{[m[38;2;234;234;234m[48;2;0;0;0m[56;1H[38;2;66;66;66m 165 [m[38;2;234;234;234m[48;2;0;0;0m[48;2;66;66;66m}[m[38;2;234;234;234m[48;2;0;0;0m[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H165,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[32;28H{[55;6H}
[38;2;66;66;66m 166 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H166,0-1[8C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 167 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mimpl[m[38;2;234;234;234m[48;2;0;0;0m GlyphCache {[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H167,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 168 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mnew[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mL[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m([82C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H168,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 169 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m rasterizer: Rasterizer,[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H169,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 170 [m[38;2;234;234;234m[48;2;0;0;0m[8Cconfig: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mConfig,[76C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H170,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 171 [m[38;2;234;234;234m[48;2;0;0;0m[8Cloader: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m L[78C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H171,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 172 [m[38;2;234;234;234m[48;2;0;0;0m    ) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mResult[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mGlyphCache, [38;2;237;158;86mfont::[m[38;2;234;234;234m[48;2;0;0;0mError[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m[60C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H172,1[10C8%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 173 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mwhere[m[38;2;234;234;234m[48;2;0;0;0m L: LoadGlyph[74C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H173,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 174 [m[38;2;234;234;234m[48;2;0;0;0m    {[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H174,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 175 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m font [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m config.[38;2;140;182;225mfont[m[38;2;234;234;234m[48;2;0;0;0m();[67C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H175,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 176 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m size [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m font.[38;2;140;182;225msize[m[38;2;234;234;234m[48;2;0;0;0m();[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H176,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 177 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m glyph_offset [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0mfont.[38;2;140;182;225mglyph_offset[m[38;2;234;234;234m[48;2;0;0;0m();[52C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H177,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 178 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H178,0-1[8C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 179 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mmake_desc[m[38;2;234;234;234m[48;2;0;0;0m([79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H179,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 180 [m[38;2;234;234;234m[48;2;0;0;0m[12Cdesc: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86mconfig::[m[38;2;234;234;234m[48;2;0;0;0mFontDescription,[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H180,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 181 [m[38;2;234;234;234m[48;2;0;0;0m[12Cslant: [38;2;237;158;86mfont::[m[38;2;234;234;234m[48;2;0;0;0mSlant,[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H181,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 182 [m[38;2;234;234;234m[48;2;0;0;0m[12Cweight: [38;2;237;158;86mfont::[m[38;2;234;234;234m[48;2;0;0;0mWeight,[67C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H182,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 183 [m[38;2;234;234;234m[48;2;0;0;0m[8C) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m FontDesc[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H183,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 184 [m[38;2;234;234;234m[48;2;0;0;0m[8C{[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H184,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 185 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m style [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mSome[m[38;2;234;234;234m[48;2;0;0;0m([38;2;207;171;224mref[m[38;2;234;234;234m[48;2;0;0;0m spec) [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m desc.style {[40C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H185,1[10C9%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 186 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;237;158;86mfont::Style::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mSpecific[m[38;2;234;234;234m[48;2;0;0;0m(spec.[38;2;140;182;225mto_owned[m[38;2;234;234;234m[48;2;0;0;0m())[46C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H186,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 187 [m[38;2;234;234;234m[48;2;0;0;0m[12C} [38;2;207;171;224melse[m[38;2;234;234;234m[48;2;0;0;0m {[80C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H187,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 188 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;237;158;86mfont::Style::[m[38;2;234;234;234m[48;2;0;0;0mDescription {slant:slant, weight:weight}[31C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H188,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 189 [m[38;2;234;234;234m[48;2;0;0;0m[12C};[86C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H189,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 190 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mFontDesc::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mnew[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mdesc.family[..], style)[50C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H190,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 191 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H191,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 192 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H192,0-1[7C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 193 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;118;120;118m// Load regular font[m[38;2;234;234;234m[48;2;0;0;0m[72C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H193,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 194 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m regular_desc [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mmake_desc[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mfont.normal, [38;2;237;158;86mfont::Slant::[m[38;2;234;234;234m[48;2;0;0;0mNormal, [38;2;237;158;86mfont::Weight::[m[38;2;234;234;234m[48;2;0;0;0mNormal);      [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H194,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 195 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H195,0-1[7C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 196 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m regular [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m rasterizer[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H196,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 197 [m[38;2;234;234;234m[48;2;0;0;0m[12C.[38;2;140;182;225mload_font[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mregular_desc, size)[38;2;129;202;191m?[m[38;2;234;234;234m[48;2;0;0;0m;[55C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H197,1[9C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 198 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H198,0-1[7C10%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 199 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;118;120;118m// helper to load a description if it is not the regular_desc[m[38;2;234;234;234m[48;2;0;0;0m[31C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H199,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 200 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m load_or_regular [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m|[m[38;2;234;234;234m[48;2;0;0;0mdesc:FontDesc, rasterizer: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m Rasterizer[38;2;129;202;191m|[m[38;2;234;234;234m[48;2;0;0;0m {[24C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H200,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 201 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m desc [38;2;129;202;191m==[m[38;2;234;234;234m[48;2;0;0;0m regular_desc {[63C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H201,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 202 [m[38;2;234;234;234m[48;2;0;0;0m[16Cregular[77C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H202,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 203 [m[38;2;234;234;234m[48;2;0;0;0m[12C} [38;2;207;171;224melse[m[38;2;234;234;234m[48;2;0;0;0m {[80C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H203,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 204 [m[38;2;234;234;234m[48;2;0;0;0m[16Crasterizer.[38;2;140;182;225mload_font[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mdesc, size).[38;2;140;182;225munwrap_or_else[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m|[m[38;2;234;234;234m[48;2;0;0;0m_[38;2;129;202;191m|[m[38;2;234;234;234m[48;2;0;0;0m regular)[23C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H204,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 205 [m[38;2;234;234;234m[48;2;0;0;0m[12C}[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H205,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 206 [m[38;2;234;234;234m[48;2;0;0;0m[8C};[90C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H206,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 207 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H207,0-1[7C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 208 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;118;120;118m// Load bold font[m[38;2;234;234;234m[48;2;0;0;0m[75C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H208,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 209 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m bold_desc [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mmake_desc[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mfont.bold, [38;2;237;158;86mfont::Slant::[m[38;2;234;234;234m[48;2;0;0;0mNormal, [38;2;237;158;86mfont::Weight::[m[38;2;234;234;234m[48;2;0;0;0mBold);[13C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H209,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 210 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H210,0-1[7C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 211 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m bold [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mload_or_regular[m[38;2;234;234;234m[48;2;0;0;0m(bold_desc, [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m rasterizer);[37C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H211,1[9C11%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 212 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H212,0-1[7C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 213 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;118;120;118m// Load italic font[m[38;2;234;234;234m[48;2;0;0;0m[73C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H213,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 214 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m italic_desc [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mmake_desc[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mfont.italic, [38;2;237;158;86mfont::Slant::[m[38;2;234;234;234m[48;2;0;0;0mItalic, [38;2;237;158;86mfont::Weight::[m[38;2;234;234;234m[48;2;0;0;0mNormal);[7C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H214,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 215 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H215,0-1[7C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 216 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m italic [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mload_or_regular[m[38;2;234;234;234m[48;2;0;0;0m(italic_desc, [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m rasterizer);[33C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H216,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 217 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H217,0-1[7C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 218 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;118;120;118m// Need to load at least one glyph for the face before calling metrics.[m[38;2;234;234;234m[48;2;0;0;0m[21C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H218,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 219 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;118;120;118m// The glyph requested here ('m' at the time of writing) has no special[m[38;2;234;234;234m[48;2;0;0;0m[21C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H219,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 220 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;118;120;118m// meaning.[m[38;2;234;234;234m[48;2;0;0;0m[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H220,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 221 [m[38;2;234;234;234m[48;2;0;0;0m[8Crasterizer.[38;2;140;182;225mget_glyph[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mGlyphKey { font_key: regular, c: [38;2;237;158;86m'm'[m[38;2;234;234;234m[48;2;0;0;0m, size: font.[38;2;140;182;225msize[m[38;2;234;234;234m[48;2;0;0;0m() })[38;2;129;202;191m?[m[38;2;234;234;234m[48;2;0;0;0m;[10C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H221,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 222 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m metrics [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m rasterizer.[38;2;140;182;225mmetrics[m[38;2;234;234;234m[48;2;0;0;0m(regular)[38;2;129;202;191m?[m[38;2;234;234;234m[48;2;0;0;0m;[49C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H222,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 223 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H223,0-1[7C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 224 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m cache [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m GlyphCache {[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H224,1[9C12%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 225 [m[38;2;234;234;234m[48;2;0;0;0m[12Ccache: [38;2;237;158;86mHashMap::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mdefault[m[38;2;234;234;234m[48;2;0;0;0m(),[62C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H225,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 226 [m[38;2;234;234;234m[48;2;0;0;0m[12Crasterizer: rasterizer,[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H226,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 227 [m[38;2;234;234;234m[48;2;0;0;0m[12Cfont_size: font.[38;2;140;182;225msize[m[38;2;234;234;234m[48;2;0;0;0m(),[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H227,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 228 [m[38;2;234;234;234m[48;2;0;0;0m[12Cfont_key: regular,[70C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H228,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 229 [m[38;2;234;234;234m[48;2;0;0;0m[12Cbold_key: bold,[73C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H229,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 230 [m[38;2;234;234;234m[48;2;0;0;0m[12Citalic_key: italic,[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H230,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 231 [m[38;2;234;234;234m[48;2;0;0;0m[12Cglyph_offset: glyph_offset,[61C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H231,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 232 [m[38;2;234;234;234m[48;2;0;0;0m[12Cmetrics: metrics[72C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H232,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 233 [m[38;2;234;234;234m[48;2;0;0;0m[8C};[90C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H233,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 234 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H234,0-1[7C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 235 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;223;101;102mmacro_rules![m[38;2;234;234;234m[48;2;0;0;0m load_glyphs_for_font {[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H235,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 236 [m[38;2;234;234;234m[48;2;0;0;0m[12C([38;2;237;158;86m$font[m[38;2;234;234;234m[48;2;0;0;0m:expr) [38;2;129;202;191m=>[m[38;2;234;234;234m[48;2;0;0;0m {[71C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H236,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 237 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mfor[m[38;2;234;234;234m[48;2;0;0;0m i [38;2;207;171;224min[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mRangeInclusive::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mnew[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m32u8[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86m128u8[m[38;2;234;234;234m[48;2;0;0;0m) {[41C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H237,1[9C13%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 238 [m[38;2;234;234;234m[48;2;0;0;0m[20Ccache.[38;2;140;182;225mget[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mGlyphKey {[59C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H238,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 239 [m[38;2;234;234;234m[48;2;0;0;0m[24Cfont_key: [38;2;237;158;86m$font[m[38;2;234;234;234m[48;2;0;0;0m,[60C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H239,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 240 [m[38;2;234;234;234m[48;2;0;0;0m[24Cc: i [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mchar[m[38;2;234;234;234m[48;2;0;0;0m,[63C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H240,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 241 [m[38;2;234;234;234m[48;2;0;0;0m[24Csize: font.[38;2;140;182;225msize[m[38;2;234;234;234m[48;2;0;0;0m()[59C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H241,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 242 [m[38;2;234;234;234m[48;2;0;0;0m[20C}, loader);[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H242,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 243 [m[38;2;234;234;234m[48;2;0;0;0m[16C}[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H243,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 244 [m[38;2;234;234;234m[48;2;0;0;0m[12C}[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H244,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 245 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H245,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 246 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H246,0-1[7C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 247 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;223;101;102mload_glyphs_for_font![m[38;2;234;234;234m[48;2;0;0;0m(regular);[61C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H247,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 248 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;223;101;102mload_glyphs_for_font![m[38;2;234;234;234m[48;2;0;0;0m(bold);[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H248,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 249 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;223;101;102mload_glyphs_for_font![m[38;2;234;234;234m[48;2;0;0;0m(italic);[62C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H249,1[9C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 250 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H250,0-1[7C14%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 251 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;237;158;86mOk[m[38;2;234;234;234m[48;2;0;0;0m(cache)[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H251,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 252 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H252,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 253 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H253,0-1[7C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 254 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mfont_metrics[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mfont::[m[38;2;234;234;234m[48;2;0;0;0mMetrics {[51C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H254,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 255 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.rasterizer[77C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H255,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 256 [m[38;2;234;234;234m[48;2;0;0;0m[12C.[38;2;140;182;225mmetrics[m[38;2;234;234;234m[48;2;0;0;0m([38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.font_key)[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H256,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 257 [m[38;2;234;234;234m[48;2;0;0;0m[12C.[38;2;140;182;225mexpect[m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"metrics load since font is loaded at glyph cache creation"[m[38;2;234;234;234m[48;2;0;0;0m)[20C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H257,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 258 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H258,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 259 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H259,0-1[7C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 260 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mget[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m, L[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, glyph_key: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mGlyphKey, loader: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m L) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m Glyph[14C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H260,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 261 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mwhere[m[38;2;234;234;234m[48;2;0;0;0m L: LoadGlyph[74C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H261,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 262 [m[38;2;234;234;234m[48;2;0;0;0m    {[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H262,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 263 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m glyph_offset [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.glyph_offset;[55C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H263,1[9C15%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 264 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m rasterizer [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.rasterizer;[54C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H264,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 265 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m metrics [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.metrics;[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H265,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 266 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.cache[82C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H266,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 267 [m[38;2;234;234;234m[48;2;0;0;0m[12C.[38;2;140;182;225mentry[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0mglyph_key)[70C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H267,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 268 [m[38;2;234;234;234m[48;2;0;0;0m[12C.[38;2;140;182;225mor_insert_with[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m||[m[38;2;234;234;234m[48;2;0;0;0m {[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H268,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 269 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m rasterized [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m rasterizer.[38;2;140;182;225mget_glyph[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mglyph_key)[31C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H269,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 270 [m[38;2;234;234;234m[48;2;0;0;0m[20C.[38;2;140;182;225munwrap_or_else[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m|[m[38;2;234;234;234m[48;2;0;0;0m_[38;2;129;202;191m|[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mDefault[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mdefault[m[38;2;234;234;234m[48;2;0;0;0m());[40C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H270,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 271 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H271,0-1[7C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 272 [m[38;2;234;234;234m[48;2;0;0;0m[16Crasterized.left [38;2;129;202;191m+=[m[38;2;234;234;234m[48;2;0;0;0m glyph_offset.x [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mi32[m[38;2;234;234;234m[48;2;0;0;0m;[43C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H272,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 273 [m[38;2;234;234;234m[48;2;0;0;0m[16Crasterized.top [38;2;129;202;191m+=[m[38;2;234;234;234m[48;2;0;0;0m glyph_offset.y [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mi32[m[38;2;234;234;234m[48;2;0;0;0m;[44C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H273,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 274 [m[38;2;234;234;234m[48;2;0;0;0m[16Crasterized.top [38;2;129;202;191m-=[m[38;2;234;234;234m[48;2;0;0;0m metrics.descent [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mi32[m[38;2;234;234;234m[48;2;0;0;0m;[43C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H274,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 275 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H275,0-1[7C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 276 [m[38;2;234;234;234m[48;2;0;0;0m[16Cloader.[38;2;140;182;225mload_glyph[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mrasterized)[54C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H276,1[9C16%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 277 [m[38;2;234;234;234m[48;2;0;0;0m[12C})[86C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H277,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 278 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H278,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 279 [m[38;2;234;234;234m[48;2;0;0;0m}[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H279,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 280 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H280,0-1[7C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 281 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m#[derive([m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mDebug[m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m)][m[38;2;234;234;234m[48;2;0;0;0m[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H281,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 282 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m#[repr(C)][m[38;2;234;234;234m[48;2;0;0;0m[90C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H282,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 283 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mstruct[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mInstanceData[m[38;2;234;234;234m[48;2;0;0;0m {[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H283,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 284 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m// coords[m[38;2;234;234;234m[48;2;0;0;0m[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H284,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 285 [m[38;2;234;234;234m[48;2;0;0;0m    col: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H285,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 286 [m[38;2;234;234;234m[48;2;0;0;0m    row: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H286,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 287 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m// glyph offset[m[38;2;234;234;234m[48;2;0;0;0m[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H287,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 288 [m[38;2;234;234;234m[48;2;0;0;0m    left: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[86C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H288,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 289 [m[38;2;234;234;234m[48;2;0;0;0m    top: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H289,1[9C17%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 290 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m// glyph scale[m[38;2;234;234;234m[48;2;0;0;0m[82C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H290,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 291 [m[38;2;234;234;234m[48;2;0;0;0m    width: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[85C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H291,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 292 [m[38;2;234;234;234m[48;2;0;0;0m    height: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H292,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 293 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m// uv offset[m[38;2;234;234;234m[48;2;0;0;0m[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H293,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 294 [m[38;2;234;234;234m[48;2;0;0;0m    uv_left: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H294,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 295 [m[38;2;234;234;234m[48;2;0;0;0m    uv_bot: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H295,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 296 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m// uv scale[m[38;2;234;234;234m[48;2;0;0;0m[85C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H296,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 297 [m[38;2;234;234;234m[48;2;0;0;0m    uv_width: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[82C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H297,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 298 [m[38;2;234;234;234m[48;2;0;0;0m    uv_height: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H298,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 299 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m// [m[38;2;234;234;234m[48;2;0;0;0m[38;2;0;0;0m[48;2;236;206;88mcolor[m[38;2;234;234;234m[48;2;0;0;0m[88C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H299,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 300 [m[38;2;234;234;234m[48;2;0;0;0m    r: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[89C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H300,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 301 [m[38;2;234;234;234m[48;2;0;0;0m    g: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[89C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H301,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 302 [m[38;2;234;234;234m[48;2;0;0;0m    b: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[89C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H302,1[9C18%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 303 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m// background [m[38;2;234;234;234m[48;2;0;0;0m[38;2;0;0;0m[48;2;236;206;88mcolor[m[38;2;234;234;234m[48;2;0;0;0m[77C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H303,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 304 [m[38;2;234;234;234m[48;2;0;0;0m    bg_r: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[86C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H304,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 305 [m[38;2;234;234;234m[48;2;0;0;0m    bg_g: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[86C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H305,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 306 [m[38;2;234;234;234m[48;2;0;0;0m    bg_b: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[86C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H306,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[32;26H[48;2;66;66;66m{[m[38;2;234;234;234m[48;2;0;0;0m[56;1H[38;2;66;66;66m 307 [m[38;2;234;234;234m[48;2;0;0;0m[48;2;66;66;66m}[m[38;2;234;234;234m[48;2;0;0;0m[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H307,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[31;26H{[55;6H}
[38;2;66;66;66m 308 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H308,0-1[7C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 309 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m#[derive([m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mDebug[m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m)][m[38;2;234;234;234m[48;2;0;0;0m[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H309,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 310 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstruct[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mQuadRenderer[m[38;2;234;234;234m[48;2;0;0;0m {[75C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H310,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 311 [m[38;2;234;234;234m[48;2;0;0;0m    program: ShaderProgram,[73C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H311,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 312 [m[38;2;234;234;234m[48;2;0;0;0m    vao: GLuint,[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H312,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 313 [m[38;2;234;234;234m[48;2;0;0;0m    vbo: GLuint,[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H313,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 314 [m[38;2;234;234;234m[48;2;0;0;0m    ebo: GLuint,[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H314,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 315 [m[38;2;234;234;234m[48;2;0;0;0m    vbo_instance: GLuint,[75C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H315,1[9C19%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 316 [m[38;2;234;234;234m[48;2;0;0;0m    atlas: [38;2;207;171;224mVec[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mAtlas[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m,[78C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H316,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 317 [m[38;2;234;234;234m[48;2;0;0;0m    active_tex: GLuint,[77C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H317,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 318 [m[38;2;234;234;234m[48;2;0;0;0m    batch: Batch,[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H318,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 319 [m[38;2;234;234;234m[48;2;0;0;0m    rx: [38;2;237;158;86mmpsc::[m[38;2;234;234;234m[48;2;0;0;0mReceiver[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mMsg[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m,[72C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H319,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[46;30H[48;2;66;66;66m{[m[38;2;234;234;234m[48;2;0;0;0m[56;1H[38;2;66;66;66m 320 [m[38;2;234;234;234m[48;2;0;0;0m[48;2;66;66;66m}[m[38;2;234;234;234m[48;2;0;0;0m[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H320,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[45;30H{[55;6H}
[38;2;66;66;66m 321 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H321,0-1[7C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 322 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m#[derive([m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mDebug[m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m)][m[38;2;234;234;234m[48;2;0;0;0m[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H322,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 323 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstruct[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mRenderApi[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m {[74C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H323,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 324 [m[38;2;234;234;234m[48;2;0;0;0m    active_tex: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m GLuint,[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H324,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 325 [m[38;2;234;234;234m[48;2;0;0;0m    batch: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m Batch,[75C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H325,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 326 [m[38;2;234;234;234m[48;2;0;0;0m    atlas: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mVec[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mAtlas[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m,[70C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H326,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 327 [m[38;2;234;234;234m[48;2;0;0;0m    program: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m ShaderProgram,[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H327,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 328 [m[38;2;234;234;234m[48;2;0;0;0m    config: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m Config,[77C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H328,1[9C20%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 329 [m[38;2;234;234;234m[48;2;0;0;0m    visual_bell_intensity: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m[70C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H329,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[49;31H[48;2;66;66;66m{[m[38;2;234;234;234m[48;2;0;0;0m[56;1H[38;2;66;66;66m 330 [m[38;2;234;234;234m[48;2;0;0;0m[48;2;66;66;66m}[m[38;2;234;234;234m[48;2;0;0;0m[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H330,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[48;31H{[55;6H}
[38;2;66;66;66m 331 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H331,0-1[7C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 332 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m#[derive([m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mDebug[m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m)][m[38;2;234;234;234m[48;2;0;0;0m[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H332,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 333 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstruct[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mLoaderApi[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m {[74C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H333,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 334 [m[38;2;234;234;234m[48;2;0;0;0m    active_tex: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m GLuint,[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H334,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 335 [m[38;2;234;234;234m[48;2;0;0;0m    atlas: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mVec[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mAtlas[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m,[70C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H335,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[53;31H[48;2;66;66;66m{[m[38;2;234;234;234m[48;2;0;0;0m


[38;2;66;66;66m 336 [m[38;2;234;234;234m[48;2;0;0;0m[48;2;66;66;66m}[m[38;2;234;234;234m[48;2;0;0;0m[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H336,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[52;31H{[55;6H}
[38;2;66;66;66m 337 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H337,0-1[7C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 338 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m#[derive([m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mDebug[m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m)][m[38;2;234;234;234m[48;2;0;0;0m[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H338,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 339 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstruct[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mPackedVertex[m[38;2;234;234;234m[48;2;0;0;0m {[75C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H339,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 340 [m[38;2;234;234;234m[48;2;0;0;0m    x: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[89C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H340,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 341 [m[38;2;234;234;234m[48;2;0;0;0m    y: [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[89C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H341,1[9C21%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[53;30H[48;2;66;66;66m{[m[38;2;234;234;234m[48;2;0;0;0m


[38;2;66;66;66m 342 [m[38;2;234;234;234m[48;2;0;0;0m[48;2;66;66;66m}[m[38;2;234;234;234m[48;2;0;0;0m[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H342,1[9C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[52;30H{[55;6H}
[38;2;66;66;66m 343 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H343,0-1[7C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 344 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m#[derive([m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mDebug[m[38;2;234;234;234m[48;2;0;0;0m[38;2;223;101;102m)][m[38;2;234;234;234m[48;2;0;0;0m[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H344,1[9C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 345 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mstruct[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mBatch[m[38;2;234;234;234m[48;2;0;0;0m {[82C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H345,1[9C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 346 [m[38;2;234;234;234m[48;2;0;0;0m    tex: GLuint,[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H346,1[9C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 347 [m[38;2;234;234;234m[48;2;0;0;0m    instances: [38;2;207;171;224mVec[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mInstanceData[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m,[67C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H347,1[9C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[53;23H[48;2;66;66;66m{[m[38;2;234;234;234m[48;2;0;0;0m


[38;2;66;66;66m 348 [m[38;2;234;234;234m[48;2;0;0;0m[48;2;66;66;66m}[m[38;2;234;234;234m[48;2;0;0;0m[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H348,1[9C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[52;23H{[55;6H}
[38;2;66;66;66m 349 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H349,0-1[7C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 350 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mimpl[m[38;2;234;234;234m[48;2;0;0;0m Batch {[88C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H350,1[9C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 351 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;223;101;102m#[inline][m[38;2;234;234;234m[48;2;0;0;0m[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H351,1[9C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 352 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mnew[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m Batch {[73C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H352,1[9C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 353 [m[38;2;234;234;234m[48;2;0;0;0m[8CBatch {[85C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H353,1[9C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 354 [m[38;2;234;234;234m[48;2;0;0;0m[12Ctex: [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m,[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H354,1[9C22%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 355 [m[38;2;234;234;234m[48;2;0;0;0m[12Cinstances: [38;2;207;171;224mVec[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mwith_capacity[m[38;2;234;234;234m[48;2;0;0;0m(BATCH_MAX),[47C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H355,1[9C23%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 356 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H356,1[9C23%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 357 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H357,1[9C23%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 358 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H358,0-1[7C23%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 359 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225madd_item[m[38;2;234;234;234m[48;2;0;0;0m([80C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H359,1[9C23%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 360 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m,[82C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H360,1[9C23%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 361 [m[38;2;234;234;234m[48;2;0;0;0m[8Ccell: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mRenderableCell,[70C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H361,1[9C23%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 362 [m[38;2;234;234;234m[48;2;0;0;0m[8Cglyph: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mGlyph,[78C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H362,1[9C23%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 363 [m[38;2;234;234;234m[48;2;0;0;0m    ) {[93C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H363,1[9C23%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 364 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.[38;2;140;182;225mis_empty[m[38;2;234;234;234m[48;2;0;0;0m() {[72C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H364,1[9C23%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 365 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.tex [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m glyph.tex_id;[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H365,1[9C23%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 366 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H366,1[9C23%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 367 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H367,0-1[7C23%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 368 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.instances.[38;2;140;182;225mpush[m[38;2;234;234;234m[48;2;0;0;0m(InstanceData {[58C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H368,1[9C24%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 369 [m[38;2;234;234;234m[48;2;0;0;0m[12Ccol: cell.column.[38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[62C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H369,1[9C24%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 370 [m[38;2;234;234;234m[48;2;0;0;0m[12Crow: cell.line.[38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H370,1[9C24%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 371 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H371,0-1[7C24%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 372 [m[38;2;234;234;234m[48;2;0;0;0m[12Ctop: glyph.top,[73C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H372,1[9C24%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 373 [m[38;2;234;234;234m[48;2;0;0;0m[12Cleft: glyph.left,[71C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H373,1[9C24%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 374 [m[38;2;234;234;234m[48;2;0;0;0m[12Cwidth: glyph.width,[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H374,1[9C24%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 375 [m[38;2;234;234;234m[48;2;0;0;0m[12Cheight: glyph.height,[67C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H375,1[9C24%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 376 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H376,0-1[7C24%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 377 [m[38;2;234;234;234m[48;2;0;0;0m[12Cuv_bot: glyph.uv_bot,[67C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H377,1[9C24%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 378 [m[38;2;234;234;234m[48;2;0;0;0m[12Cuv_left: glyph.uv_left,[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H378,1[9C24%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 379 [m[38;2;234;234;234m[48;2;0;0;0m[12Cuv_width: glyph.uv_width,[63C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H379,1[9C24%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 380 [m[38;2;234;234;234m[48;2;0;0;0m[12Cuv_height: glyph.uv_height,[61C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H380,1[9C24%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 381 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H381,0-1[7C25%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 382 [m[38;2;234;234;234m[48;2;0;0;0m[12Cr: cell.fg.r [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H382,1[9C25%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 383 [m[38;2;234;234;234m[48;2;0;0;0m[12Cg: cell.fg.g [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H383,1[9C25%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 384 [m[38;2;234;234;234m[48;2;0;0;0m[12Cb: cell.fg.b [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H384,1[9C25%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 385 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H385,0-1[7C25%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 386 [m[38;2;234;234;234m[48;2;0;0;0m[12Cbg_r: cell.bg.r [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H386,1[9C25%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 387 [m[38;2;234;234;234m[48;2;0;0;0m[12Cbg_g: cell.bg.g [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H387,1[9C25%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 388 [m[38;2;234;234;234m[48;2;0;0;0m[12Cbg_b: cell.bg.b [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m,[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H388,1[9C25%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 389 [m[38;2;234;234;234m[48;2;0;0;0m[8C});[89C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H389,1[9C25%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 390 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H390,1[9C25%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 391 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H391,0-1[7C25%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 392 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;223;101;102m#[inline][m[38;2;234;234;234m[48;2;0;0;0m[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H392,1[9C25%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 393 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mfull[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mbool[m[38;2;234;234;234m[48;2;0;0;0m {[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H393,1[9C25%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 394 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.[38;2;140;182;225mcapacity[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191m==[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.[38;2;140;182;225mlen[m[38;2;234;234;234m[48;2;0;0;0m()[63C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H394,1[9C26%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 395 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H395,1[9C26%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 396 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H396,0-1[7C26%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 397 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;223;101;102m#[inline][m[38;2;234;234;234m[48;2;0;0;0m[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H397,1[9C26%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 398 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mlen[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224musize[m[38;2;234;234;234m[48;2;0;0;0m {[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H398,1[9C26%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 399 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.instances.[38;2;140;182;225mlen[m[38;2;234;234;234m[48;2;0;0;0m()[72C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H399,1[9C26%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 400 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H400,1[9C26%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 401 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H401,0-1[7C26%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 402 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;223;101;102m#[inline][m[38;2;234;234;234m[48;2;0;0;0m[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H402,1[9C26%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 403 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mcapacity[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224musize[m[38;2;234;234;234m[48;2;0;0;0m {[63C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H403,1[9C26%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 404 [m[38;2;234;234;234m[48;2;0;0;0m[8CBATCH_MAX[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H404,1[9C26%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 405 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H405,1[9C26%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 406 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H406,0-1[7C26%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 407 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;223;101;102m#[inline][m[38;2;234;234;234m[48;2;0;0;0m[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H407,1[9C27%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 408 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mis_empty[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mbool[m[38;2;234;234;234m[48;2;0;0;0m {[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H408,1[9C27%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 409 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.[38;2;140;182;225mlen[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191m==[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m[77C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H409,1[9C27%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 410 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H410,1[9C27%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 411 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H411,0-1[7C27%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 412 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;223;101;102m#[inline][m[38;2;234;234;234m[48;2;0;0;0m[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H412,1[9C27%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 413 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225msize[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224musize[m[38;2;234;234;234m[48;2;0;0;0m {[67C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H413,1[9C27%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 414 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.[38;2;140;182;225mlen[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225msize_of[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mInstanceData[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m()[54C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H414,1[9C27%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 415 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H415,1[9C27%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 416 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H416,0-1[7C27%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 417 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mclear[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m) {[71C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H417,1[9C27%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 418 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.tex [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m;[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H418,1[9C27%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 419 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.instances.[38;2;140;182;225mclear[m[38;2;234;234;234m[48;2;0;0;0m();[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H419,1[9C27%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 420 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H420,1[9C28%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 421 [m[38;2;234;234;234m[48;2;0;0;0m}[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H421,1[9C28%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 422 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H422,0-1[7C28%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 423 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m/// Maximum items to be drawn in a batch.[m[38;2;234;234;234m[48;2;0;0;0m[59C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H423,1[9C28%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 424 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mconst[m[38;2;234;234;234m[48;2;0;0;0m BATCH_MAX: [38;2;207;171;224musize[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m65_536[m[38;2;234;234;234m[48;2;0;0;0m;[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H424,1[9C28%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 425 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mconst[m[38;2;234;234;234m[48;2;0;0;0m ATLAS_SIZE: [38;2;207;171;224mi32[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m1024[m[38;2;234;234;234m[48;2;0;0;0m;[71C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H425,1[9C28%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 426 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H426,0-1[7C28%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 427 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mimpl[m[38;2;234;234;234m[48;2;0;0;0m QuadRenderer {[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H427,1[9C28%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 428 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m// [m[38;2;234;234;234m[48;2;0;0;0m[1m[38;2;236;206;88mTODO[m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m should probably hand this a transform instead of width/height[m[38;2;234;234;234m[48;2;0;0;0m[27C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H428,1[9C28%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 429 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mnew[m[38;2;234;234;234m[48;2;0;0;0m(config: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mConfig, size: Size[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mPixels[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mu32[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>>[m[38;2;234;234;234m[48;2;0;0;0m) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mResult[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mQuadRenderer, Error[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m {[11C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H429,1[9C28%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 430 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m program [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mShaderProgram::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mnew[m[38;2;234;234;234m[48;2;0;0;0m(config, size)[38;2;129;202;191m?[m[38;2;234;234;234m[48;2;0;0;0m;[44C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H430,1[9C28%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 431 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H431,0-1[7C28%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 432 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m vao: GLuint [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m;[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H432,1[9C28%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 433 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m vbo: GLuint [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m;[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H433,1[9C29%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 434 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m ebo: GLuint [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m;[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H434,1[9C29%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 435 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H435,0-1[7C29%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 436 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m vbo_instance: GLuint [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m;[59C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H436,1[9C29%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 437 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H437,0-1[7C29%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 438 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224munsafe[m[38;2;234;234;234m[48;2;0;0;0m {[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H438,1[9C29%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 439 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mEnable[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mBLEND);[66C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H439,1[9C29%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 440 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBlendFunc[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mSRC1_[38;2;0;0;0m[48;2;236;206;88mCOLOR[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mONE_MINUS_SRC1_[38;2;0;0;0m[48;2;236;206;88mCOLOR[m[38;2;234;234;234m[48;2;0;0;0m);[32C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H440,1[9C29%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 441 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mEnable[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mMULTISAMPLE);[60C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H441,1[9C29%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 442 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H442,0-1[7C29%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 443 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mGenVertexArrays[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m1[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m vao);[55C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H443,1[9C29%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 444 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mGenBuffers[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m1[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m vbo);[60C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H444,1[9C29%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 445 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mGenBuffers[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m1[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m ebo);[60C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H445,1[9C29%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 446 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mGenBuffers[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m1[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m vbo_instance);[51C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H446,1[9C30%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 447 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBindVertexArray[m[38;2;234;234;234m[48;2;0;0;0m(vao);[63C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H447,1[9C30%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 448 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H448,0-1[7C30%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 449 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// ----------------------------[m[38;2;234;234;234m[48;2;0;0;0m[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H449,1[9C30%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 450 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// setup vertex position buffer[m[38;2;234;234;234m[48;2;0;0;0m[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H450,1[9C30%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 451 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// ----------------------------[m[38;2;234;234;234m[48;2;0;0;0m[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H451,1[9C30%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 452 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// Top right, Bottom right, Bottom left, Top left[m[38;2;234;234;234m[48;2;0;0;0m[39C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H452,1[9C30%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 453 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m vertices [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [[72C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H453,1[9C30%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 454 [m[38;2;234;234;234m[48;2;0;0;0m[16CPackedVertex { x: [38;2;237;158;86m1.0[m[38;2;234;234;234m[48;2;0;0;0m, y: [38;2;237;158;86m1.0[m[38;2;234;234;234m[48;2;0;0;0m },[52C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H454,1[9C30%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 455 [m[38;2;234;234;234m[48;2;0;0;0m[16CPackedVertex { x: [38;2;237;158;86m1.0[m[38;2;234;234;234m[48;2;0;0;0m, y: [38;2;237;158;86m0.0[m[38;2;234;234;234m[48;2;0;0;0m },[52C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H455,1[9C30%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 456 [m[38;2;234;234;234m[48;2;0;0;0m[16CPackedVertex { x: [38;2;237;158;86m0.0[m[38;2;234;234;234m[48;2;0;0;0m, y: [38;2;237;158;86m0.0[m[38;2;234;234;234m[48;2;0;0;0m },[52C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H456,1[9C30%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 457 [m[38;2;234;234;234m[48;2;0;0;0m[16CPackedVertex { x: [38;2;237;158;86m0.0[m[38;2;234;234;234m[48;2;0;0;0m, y: [38;2;237;158;86m1.0[m[38;2;234;234;234m[48;2;0;0;0m },[52C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H457,1[9C30%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 458 [m[38;2;234;234;234m[48;2;0;0;0m[12C];[86C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H458,1[9C30%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 459 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H459,0-1[7C31%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 460 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBindBuffer[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mARRAY_BUFFER, vbo);[50C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H460,1[9C31%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 461 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H461,0-1[7C31%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 462 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mVertexAttribPointer[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86m2[m[38;2;234;234;234m[48;2;0;0;0m,[59C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H462,1[9C31%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 463 [m[38;2;234;234;234m[48;2;0;0;0m[36C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mFLOAT, [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mFALSE,[43C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H463,1[9C31%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 464 [m[38;2;234;234;234m[48;2;0;0;0m[36C[38;2;140;182;225msize_of[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mPackedVertex[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mi32[m[38;2;234;234;234m[48;2;0;0;0m,[31C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H464,1[9C31%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 465 [m[38;2;234;234;234m[48;2;0;0;0m[36C[38;2;237;158;86mptr::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mnull[m[38;2;234;234;234m[48;2;0;0;0m());[51C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H465,1[9C31%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 466 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mEnableVertexAttribArray[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m);[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H466,1[9C31%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 467 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H467,0-1[7C31%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 468 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBufferData[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mARRAY_BUFFER,[56C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H468,1[9C31%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 469 [m[38;2;234;234;234m[48;2;0;0;0m[27C([38;2;140;182;225msize_of[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mPackedVertex[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m vertices.[38;2;140;182;225mlen[m[38;2;234;234;234m[48;2;0;0;0m()) [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m GLsizeiptr,[14C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H469,1[9C31%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 470 [m[38;2;234;234;234m[48;2;0;0;0m[27Cvertices.[38;2;140;182;225mas_ptr[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mconst[m[38;2;234;234;234m[48;2;0;0;0m _,[43C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H470,1[9C31%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 471 [m[38;2;234;234;234m[48;2;0;0;0m[27C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mSTATIC_DRAW);[56C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H471,1[9C31%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 472 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H472,0-1[7C32%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 473 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// ---------------------[m[38;2;234;234;234m[48;2;0;0;0m[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H473,1[9C32%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 474 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// Set up element buffer[m[38;2;234;234;234m[48;2;0;0;0m[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H474,1[9C32%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 475 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// ---------------------[m[38;2;234;234;234m[48;2;0;0;0m[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H475,1[9C32%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 476 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m indices: [[38;2;207;171;224mu32[m[38;2;234;234;234m[48;2;0;0;0m; [38;2;237;158;86m6[m[38;2;234;234;234m[48;2;0;0;0m] [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [[38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86m1[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86m3[m[38;2;234;234;234m[48;2;0;0;0m,[55C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H476,1[9C32%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 477 [m[38;2;234;234;234m[48;2;0;0;0m[37C[38;2;237;158;86m1[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86m2[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86m3[m[38;2;234;234;234m[48;2;0;0;0m];[54C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H477,1[9C32%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 478 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H478,0-1[7C32%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 479 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBindBuffer[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mELEMENT_ARRAY_BUFFER, ebo);[42C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H479,1[9C32%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 480 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBufferData[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mELEMENT_ARRAY_BUFFER,[48C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H480,1[9C32%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 481 [m[38;2;234;234;234m[48;2;0;0;0m[27C([38;2;237;158;86m6[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225msize_of[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mu32[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m()) [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224misize[m[38;2;234;234;234m[48;2;0;0;0m,[41C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H481,1[9C32%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 482 [m[38;2;234;234;234m[48;2;0;0;0m[27Cindices.[38;2;140;182;225mas_ptr[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mconst[m[38;2;234;234;234m[48;2;0;0;0m _,[44C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H482,1[9C32%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 483 [m[38;2;234;234;234m[48;2;0;0;0m[27C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mSTATIC_DRAW);[56C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H483,1[9C32%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 484 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H484,0-1[7C32%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 485 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// ----------------------------[m[38;2;234;234;234m[48;2;0;0;0m[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H485,1[9C33%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 486 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// Setup vertex instance buffer[m[38;2;234;234;234m[48;2;0;0;0m[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H486,1[9C33%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 487 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// ----------------------------[m[38;2;234;234;234m[48;2;0;0;0m[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H487,1[9C33%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 488 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBindBuffer[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mARRAY_BUFFER, vbo_instance);[41C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H488,1[9C33%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 489 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBufferData[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mARRAY_BUFFER,[56C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H489,1[9C33%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 490 [m[38;2;234;234;234m[48;2;0;0;0m[27C(BATCH_MAX [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225msize_of[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mInstanceData[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m()) [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224misize[m[38;2;234;234;234m[48;2;0;0;0m,[24C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H490,1[9C33%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 491 [m[38;2;234;234;234m[48;2;0;0;0m[27C[38;2;237;158;86mptr::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mnull[m[38;2;234;234;234m[48;2;0;0;0m(), [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mSTREAM_DRAW);[43C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H491,1[9C33%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 492 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// coords[m[38;2;234;234;234m[48;2;0;0;0m[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H492,1[9C33%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 493 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mVertexAttribPointer[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m1[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86m2[m[38;2;234;234;234m[48;2;0;0;0m,[59C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H493,1[9C33%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 494 [m[38;2;234;234;234m[48;2;0;0;0m[36C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mFLOAT, [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mFALSE,[43C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H494,1[9C33%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 495 [m[38;2;234;234;234m[48;2;0;0;0m[36C[38;2;140;182;225msize_of[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mInstanceData[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mi32[m[38;2;234;234;234m[48;2;0;0;0m,[31C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H495,1[9C33%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 496 [m[38;2;234;234;234m[48;2;0;0;0m[36C[38;2;237;158;86mptr::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mnull[m[38;2;234;234;234m[48;2;0;0;0m());[51C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H496,1[9C33%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 497 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mEnableVertexAttribArray[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m1[m[38;2;234;234;234m[48;2;0;0;0m);[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H497,1[9C33%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 498 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mVertexAttribDivisor[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m1[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86m1[m[38;2;234;234;234m[48;2;0;0;0m);[58C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H498,1[9C34%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 499 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// glyphoffset[m[38;2;234;234;234m[48;2;0;0;0m[74C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H499,1[9C34%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 500 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mVertexAttribPointer[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m2[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86m4[m[38;2;234;234;234m[48;2;0;0;0m,[59C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H500,1[9C34%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 501 [m[38;2;234;234;234m[48;2;0;0;0m[36C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mFLOAT, [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mFALSE,[43C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H501,1[9C34%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 502 [m[38;2;234;234;234m[48;2;0;0;0m[36C[38;2;140;182;225msize_of[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mInstanceData[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mi32[m[38;2;234;234;234m[48;2;0;0;0m,[31C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H502,1[9C34%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 503 [m[38;2;234;234;234m[48;2;0;0;0m[36C([38;2;237;158;86m2[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225msize_of[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m()) [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mconst[m[38;2;234;234;234m[48;2;0;0;0m _);[28C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H503,1[9C34%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 504 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mEnableVertexAttribArray[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m2[m[38;2;234;234;234m[48;2;0;0;0m);[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H504,1[9C34%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 505 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mVertexAttribDivisor[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m2[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86m1[m[38;2;234;234;234m[48;2;0;0;0m);[58C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H505,1[9C34%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 506 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// uv[m[38;2;234;234;234m[48;2;0;0;0m[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H506,1[9C34%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 507 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mVertexAttribPointer[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m3[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86m4[m[38;2;234;234;234m[48;2;0;0;0m,[59C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H507,1[9C34%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 508 [m[38;2;234;234;234m[48;2;0;0;0m[36C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mFLOAT, [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mFALSE,[43C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H508,1[9C34%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 509 [m[38;2;234;234;234m[48;2;0;0;0m[36C[38;2;140;182;225msize_of[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mInstanceData[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mi32[m[38;2;234;234;234m[48;2;0;0;0m,[31C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H509,1[9C34%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 510 [m[38;2;234;234;234m[48;2;0;0;0m[36C([38;2;237;158;86m6[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225msize_of[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m()) [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mconst[m[38;2;234;234;234m[48;2;0;0;0m _);[28C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H510,1[9C34%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 511 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mEnableVertexAttribArray[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m3[m[38;2;234;234;234m[48;2;0;0;0m);[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H511,1[9C35%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 512 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mVertexAttribDivisor[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m3[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86m1[m[38;2;234;234;234m[48;2;0;0;0m);[58C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H512,1[9C35%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 513 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// [m[38;2;234;234;234m[48;2;0;0;0m[38;2;0;0;0m[48;2;236;206;88mcolor[m[38;2;234;234;234m[48;2;0;0;0m[80C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H513,1[9C35%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 514 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mVertexAttribPointer[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m4[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86m3[m[38;2;234;234;234m[48;2;0;0;0m,[59C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H514,1[9C35%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 515 [m[38;2;234;234;234m[48;2;0;0;0m[36C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mFLOAT, [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mFALSE,[43C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H515,1[9C35%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 516 [m[38;2;234;234;234m[48;2;0;0;0m[36C[38;2;140;182;225msize_of[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mInstanceData[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mi32[m[38;2;234;234;234m[48;2;0;0;0m,[31C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H516,1[9C35%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 517 [m[38;2;234;234;234m[48;2;0;0;0m[36C([38;2;237;158;86m10[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225msize_of[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m()) [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mconst[m[38;2;234;234;234m[48;2;0;0;0m _);[27C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H517,1[9C35%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 518 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mEnableVertexAttribArray[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m4[m[38;2;234;234;234m[48;2;0;0;0m);[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H518,1[9C35%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 519 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mVertexAttribDivisor[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m4[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86m1[m[38;2;234;234;234m[48;2;0;0;0m);[58C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H519,1[9C35%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 520 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// [m[38;2;234;234;234m[48;2;0;0;0m[38;2;0;0;0m[48;2;236;206;88mcolor[m[38;2;234;234;234m[48;2;0;0;0m[80C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H520,1[9C35%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 521 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mVertexAttribPointer[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m5[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86m3[m[38;2;234;234;234m[48;2;0;0;0m,[59C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H521,1[9C35%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 522 [m[38;2;234;234;234m[48;2;0;0;0m[36C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mFLOAT, [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mFALSE,[43C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H522,1[9C35%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 523 [m[38;2;234;234;234m[48;2;0;0;0m[36C[38;2;140;182;225msize_of[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mInstanceData[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mi32[m[38;2;234;234;234m[48;2;0;0;0m,[31C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H523,1[9C35%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 524 [m[38;2;234;234;234m[48;2;0;0;0m[36C([38;2;237;158;86m13[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225msize_of[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m()) [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mconst[m[38;2;234;234;234m[48;2;0;0;0m _);[27C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H524,1[9C36%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 525 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mEnableVertexAttribArray[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m5[m[38;2;234;234;234m[48;2;0;0;0m);[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H525,1[9C36%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 526 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mVertexAttribDivisor[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m5[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86m1[m[38;2;234;234;234m[48;2;0;0;0m);[58C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H526,1[9C36%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 527 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H527,0-1[7C36%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 528 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBindVertexArray[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m);[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H528,1[9C36%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 529 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBindBuffer[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mARRAY_BUFFER, [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m);[52C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H529,1[9C36%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 530 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H530,1[9C36%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 531 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H531,0-1[7C36%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 532 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m (msg_tx, msg_rx) [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mmpsc::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mchannel[m[38;2;234;234;234m[48;2;0;0;0m();[53C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H532,1[9C36%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 533 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H533,0-1[7C36%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 534 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m [38;2;223;101;102mcfg![m[38;2;234;234;234m[48;2;0;0;0m(feature [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;197;209;92m"live-shader-reload"[m[38;2;234;234;234m[48;2;0;0;0m) {[51C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H534,1[9C36%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 535 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86m::std::thread::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mspawn[m[38;2;234;234;234m[48;2;0;0;0m([38;2;207;171;224mmove[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m||[m[38;2;234;234;234m[48;2;0;0;0m {[58C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H535,1[9C36%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 536 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m (tx, rx) [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m::std::sync::mpsc::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mchannel[m[38;2;234;234;234m[48;2;0;0;0m();[40C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H536,1[9C36%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 537 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m watcher [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mWatcher::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mnew[m[38;2;234;234;234m[48;2;0;0;0m(tx).[38;2;140;182;225mexpect[m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"create file watcher"[m[38;2;234;234;234m[48;2;0;0;0m);[19C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H537,1[9C37%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 538 [m[38;2;234;234;234m[48;2;0;0;0m[16Cwatcher.[38;2;140;182;225mwatch[m[38;2;234;234;234m[48;2;0;0;0m(TEXT_SHADER_F_PATH).[38;2;140;182;225mexpect[m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"watch fragment shader"[m[38;2;234;234;234m[48;2;0;0;0m);[18C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H538,1[9C37%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 539 [m[38;2;234;234;234m[48;2;0;0;0m[16Cwatcher.[38;2;140;182;225mwatch[m[38;2;234;234;234m[48;2;0;0;0m(TEXT_SHADER_V_PATH).[38;2;140;182;225mexpect[m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"watch vertex shader"[m[38;2;234;234;234m[48;2;0;0;0m);[20C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H539,1[9C37%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 540 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H540,0-1[7C37%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 541 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mloop[m[38;2;234;234;234m[48;2;0;0;0m {[78C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H541,1[9C37%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 542 [m[38;2;234;234;234m[48;2;0;0;0m[20C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m event [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m rx.[38;2;140;182;225mrecv[m[38;2;234;234;234m[48;2;0;0;0m().[38;2;140;182;225mexpect[m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"watcher event"[m[38;2;234;234;234m[48;2;0;0;0m);[34C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H542,1[9C37%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 543 [m[38;2;234;234;234m[48;2;0;0;0m[20C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m::notify::[m[38;2;234;234;234m[48;2;0;0;0mEvent { path, op } [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m event;[39C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H543,1[9C37%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 544 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H544,0-1[7C37%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 545 [m[38;2;234;234;234m[48;2;0;0;0m[20C[38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mOk[m[38;2;234;234;234m[48;2;0;0;0m(op) [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m op {[60C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H545,1[9C37%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 546 [m[38;2;234;234;234m[48;2;0;0;0m[24C[38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m op.[38;2;140;182;225mcontains[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mop::[m[38;2;234;234;234m[48;2;0;0;0mRENAME) {[48C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H546,1[9C37%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 547 [m[38;2;234;234;234m[48;2;0;0;0m[28C[38;2;207;171;224mcontinue[m[38;2;234;234;234m[48;2;0;0;0m;[63C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H547,1[9C37%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 548 [m[38;2;234;234;234m[48;2;0;0;0m[24C}[75C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H548,1[9C37%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 549 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H549,0-1[7C37%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 550 [m[38;2;234;234;234m[48;2;0;0;0m[24C[38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m op.[38;2;140;182;225mcontains[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mop::[m[38;2;234;234;234m[48;2;0;0;0mIGNORED) {[47C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H550,1[9C38%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 551 [m[38;2;234;234;234m[48;2;0;0;0m[28C[38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mSome[m[38;2;234;234;234m[48;2;0;0;0m(path) [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m path.[38;2;140;182;225mas_ref[m[38;2;234;234;234m[48;2;0;0;0m() {[37C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H551,1[9C38%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 552 [m[38;2;234;234;234m[48;2;0;0;0m[32C[38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mErr[m[38;2;234;234;234m[48;2;0;0;0m(err) [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m watcher.[38;2;140;182;225mwatch[m[38;2;234;234;234m[48;2;0;0;0m(path) {[29C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H552,1[9C38%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 553 [m[38;2;234;234;234m[48;2;0;0;0m[36C[38;2;223;101;102mwarn![m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"failed to establish watch on {:?}: {:?}"[m[38;2;234;234;234m[48;2;0;0;0m, path, err);    [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H553,1[9C38%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 554 [m[38;2;234;234;234m[48;2;0;0;0m[32C}[67C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H554,1[9C38%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 555 [m[38;2;234;234;234m[48;2;0;0;0m[28C}[71C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H555,1[9C38%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 556 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H556,0-1[7C38%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 557 [m[38;2;234;234;234m[48;2;0;0;0m[28Cmsg_tx.[38;2;140;182;225msend[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mMsg::[m[38;2;234;234;234m[48;2;0;0;0mShaderReload)[42C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H557,1[9C38%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 558 [m[38;2;234;234;234m[48;2;0;0;0m[32C.[38;2;140;182;225mexpect[m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"msg send ok"[m[38;2;234;234;234m[48;2;0;0;0m);[45C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H558,1[9C38%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 559 [m[38;2;234;234;234m[48;2;0;0;0m[24C}[75C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H559,1[9C38%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 560 [m[38;2;234;234;234m[48;2;0;0;0m[20C}[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H560,1[9C38%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 561 [m[38;2;234;234;234m[48;2;0;0;0m[16C}[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H561,1[9C38%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 562 [m[38;2;234;234;234m[48;2;0;0;0m[12C});[85C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H562,1[9C38%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 563 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H563,1[9C39%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 564 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H564,0-1[7C39%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 565 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m renderer [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m QuadRenderer {[59C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H565,1[9C39%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 566 [m[38;2;234;234;234m[48;2;0;0;0m[12Cprogram: program,[71C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H566,1[9C39%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 567 [m[38;2;234;234;234m[48;2;0;0;0m[12Cvao: vao,[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H567,1[9C39%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 568 [m[38;2;234;234;234m[48;2;0;0;0m[12Cvbo: vbo,[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H568,1[9C39%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 569 [m[38;2;234;234;234m[48;2;0;0;0m[12Cebo: ebo,[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H569,1[9C39%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 570 [m[38;2;234;234;234m[48;2;0;0;0m[12Cvbo_instance: vbo_instance,[61C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H570,1[9C39%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 571 [m[38;2;234;234;234m[48;2;0;0;0m[12Catlas: [38;2;207;171;224mVec[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mnew[m[38;2;234;234;234m[48;2;0;0;0m(),[70C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H571,1[9C39%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 572 [m[38;2;234;234;234m[48;2;0;0;0m[12Cactive_tex: [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m,[74C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H572,1[9C39%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 573 [m[38;2;234;234;234m[48;2;0;0;0m[12Cbatch: [38;2;237;158;86mBatch::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mnew[m[38;2;234;234;234m[48;2;0;0;0m(),[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H573,1[9C39%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 574 [m[38;2;234;234;234m[48;2;0;0;0m[12Crx: msg_rx,[77C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H574,1[9C39%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 575 [m[38;2;234;234;234m[48;2;0;0;0m[8C};[90C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H575,1[9C39%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 576 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H576,0-1[7C40%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 577 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m atlas [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mAtlas::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mnew[m[38;2;234;234;234m[48;2;0;0;0m(ATLAS_SIZE);[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H577,1[9C40%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 578 [m[38;2;234;234;234m[48;2;0;0;0m[8Crenderer.atlas.[38;2;140;182;225mpush[m[38;2;234;234;234m[48;2;0;0;0m(atlas);[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H578,1[9C40%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 579 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H579,0-1[7C40%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 580 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;237;158;86mOk[m[38;2;234;234;234m[48;2;0;0;0m(renderer)[80C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H580,1[9C40%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 581 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H581,1[9C40%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 582 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H582,0-1[7C40%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 583 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mwith_api[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mF, T[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m([74C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H583,1[9C40%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 584 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m,[82C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H584,1[9C40%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 585 [m[38;2;234;234;234m[48;2;0;0;0m[8Cconfig: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mConfig,[76C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H585,1[9C40%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 586 [m[38;2;234;234;234m[48;2;0;0;0m[8Cprops: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86mterm::[m[38;2;234;234;234m[48;2;0;0;0mSizeInfo,[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H586,1[9C40%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 587 [m[38;2;234;234;234m[48;2;0;0;0m[8Cvisual_bell_intensity: [38;2;207;171;224mf64[m[38;2;234;234;234m[48;2;0;0;0m,[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H587,1[9C40%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 588 [m[38;2;234;234;234m[48;2;0;0;0m[8Cfunc: F[85C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H588,1[9C40%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 589 [m[38;2;234;234;234m[48;2;0;0;0m    ) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m T[90C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H589,1[9C41%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 590 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mwhere[m[38;2;234;234;234m[48;2;0;0;0m F: [38;2;207;171;224mFnOnce[m[38;2;234;234;234m[48;2;0;0;0m(RenderApi) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m T[61C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H590,1[9C41%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 591 [m[38;2;234;234;234m[48;2;0;0;0m    {[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H591,1[9C41%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 592 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mwhile[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mOk[m[38;2;234;234;234m[48;2;0;0;0m(msg) [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.rx.[38;2;140;182;225mtry_recv[m[38;2;234;234;234m[48;2;0;0;0m() {[52C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H592,1[9C41%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 593 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mmatch[m[38;2;234;234;234m[48;2;0;0;0m msg {[77C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H593,1[9C41%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 594 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;237;158;86mMsg::[m[38;2;234;234;234m[48;2;0;0;0mShaderReload [38;2;129;202;191m=>[m[38;2;234;234;234m[48;2;0;0;0m {[62C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H594,1[9C41%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 595 [m[38;2;234;234;234m[48;2;0;0;0m[20C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.[38;2;140;182;225mreload_shaders[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mconfig, Size {[45C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H595,1[9C41%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 596 [m[38;2;234;234;234m[48;2;0;0;0m[24Cwidth: [38;2;140;182;225mPixels[m[38;2;234;234;234m[48;2;0;0;0m(props.width [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mu32[m[38;2;234;234;234m[48;2;0;0;0m),[42C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H596,1[9C41%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 597 [m[38;2;234;234;234m[48;2;0;0;0m[24Cheight: [38;2;140;182;225mPixels[m[38;2;234;234;234m[48;2;0;0;0m(props.height [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mu32[m[38;2;234;234;234m[48;2;0;0;0m)[41C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H597,1[9C41%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 598 [m[38;2;234;234;234m[48;2;0;0;0m[20C});[77C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H598,1[9C41%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 599 [m[38;2;234;234;234m[48;2;0;0;0m[16C}[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H599,1[9C41%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 600 [m[38;2;234;234;234m[48;2;0;0;0m[12C}[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H600,1[9C41%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 601 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H601,1[9C41%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 602 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H602,0-1[7C42%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 603 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224munsafe[m[38;2;234;234;234m[48;2;0;0;0m {[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H603,1[9C42%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 604 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.program.[38;2;140;182;225mactivate[m[38;2;234;234;234m[48;2;0;0;0m();[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H604,1[9C42%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 605 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.program.[38;2;140;182;225mset_term_uniforms[m[38;2;234;234;234m[48;2;0;0;0m(props);[50C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H605,1[9C42%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 606 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.program.[38;2;140;182;225mset_visual_bell[m[38;2;234;234;234m[48;2;0;0;0m(visual_bell_intensity [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m _);[31C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H606,1[9C42%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 607 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H607,0-1[7C42%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 608 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBindVertexArray[m[38;2;234;234;234m[48;2;0;0;0m([38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.vao);[58C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H608,1[9C42%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 609 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBindBuffer[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mELEMENT_ARRAY_BUFFER, [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.ebo);[37C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H609,1[9C42%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 610 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBindBuffer[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mARRAY_BUFFER, [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.vbo_instance);[36C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H610,1[9C42%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 611 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mActiveTexture[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mTEXTURE0);[56C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H611,1[9C42%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 612 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H612,1[9C42%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 613 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H613,0-1[7C42%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 614 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m res [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mfunc[m[38;2;234;234;234m[48;2;0;0;0m(RenderApi {[66C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H614,1[9C42%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 615 [m[38;2;234;234;234m[48;2;0;0;0m[12Cactive_tex: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.active_tex,[55C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H615,1[9C43%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 616 [m[38;2;234;234;234m[48;2;0;0;0m[12Cbatch: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.batch,[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H616,1[9C43%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 617 [m[38;2;234;234;234m[48;2;0;0;0m[12Catlas: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.atlas,[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H617,1[9C43%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 618 [m[38;2;234;234;234m[48;2;0;0;0m[12Cprogram: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.program,[61C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H618,1[9C43%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 619 [m[38;2;234;234;234m[48;2;0;0;0m[12Cvisual_bell_intensity: visual_bell_intensity [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m _,[38C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H619,1[9C43%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 620 [m[38;2;234;234;234m[48;2;0;0;0m[12Cconfig: config,[73C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H620,1[9C43%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 621 [m[38;2;234;234;234m[48;2;0;0;0m[8C});[89C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H621,1[9C43%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 622 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H622,0-1[7C43%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 623 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224munsafe[m[38;2;234;234;234m[48;2;0;0;0m {[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H623,1[9C43%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 624 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBindBuffer[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mELEMENT_ARRAY_BUFFER, [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m);[44C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H624,1[9C43%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 625 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBindBuffer[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mARRAY_BUFFER, [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m);[52C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H625,1[9C43%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 626 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBindVertexArray[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m);[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H626,1[9C43%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 627 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H627,0-1[7C43%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 628 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.program.[38;2;140;182;225mdeactivate[m[38;2;234;234;234m[48;2;0;0;0m();[62C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H628,1[9C44%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 629 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H629,1[9C44%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 630 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H630,0-1[7C44%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 631 [m[38;2;234;234;234m[48;2;0;0;0m[8Cres[89C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H631,1[9C44%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 632 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H632,1[9C44%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 633 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H633,0-1[7C44%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 634 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mwith_loader[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mF, T[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, func: F) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m T[47C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H634,1[9C44%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 635 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mwhere[m[38;2;234;234;234m[48;2;0;0;0m F: [38;2;207;171;224mFnOnce[m[38;2;234;234;234m[48;2;0;0;0m(LoaderApi) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m T[61C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H635,1[9C44%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 636 [m[38;2;234;234;234m[48;2;0;0;0m    {[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H636,1[9C44%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 637 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224munsafe[m[38;2;234;234;234m[48;2;0;0;0m {[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H637,1[9C44%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 638 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mActiveTexture[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mTEXTURE0);[56C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H638,1[9C44%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 639 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H639,1[9C44%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 640 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H640,0-1[7C44%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 641 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;140;182;225mfunc[m[38;2;234;234;234m[48;2;0;0;0m(LoaderApi {[76C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H641,1[9C45%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 642 [m[38;2;234;234;234m[48;2;0;0;0m[12Cactive_tex: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.active_tex,[55C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H642,1[9C45%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 643 [m[38;2;234;234;234m[48;2;0;0;0m[12Catlas: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.atlas,[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H643,1[9C45%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 644 [m[38;2;234;234;234m[48;2;0;0;0m[8C})[90C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H644,1[9C45%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 645 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H645,1[9C45%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 646 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H646,0-1[7C45%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 647 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mreload_shaders[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, config: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mConfig, size: Size[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mPixels[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mu32[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>>[m[38;2;234;234;234m[48;2;0;0;0m) {[20C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H647,1[9C45%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 648 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;223;101;102minfo![m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"Reloading shaders"[m[38;2;234;234;234m[48;2;0;0;0m);[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H648,1[9C45%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 649 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m program [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mmatch[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mShaderProgram::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mnew[m[38;2;234;234;234m[48;2;0;0;0m(config, size) {[38C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H649,1[9C45%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 650 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mOk[m[38;2;234;234;234m[48;2;0;0;0m(program) [38;2;129;202;191m=>[m[38;2;234;234;234m[48;2;0;0;0m program,[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H650,1[9C45%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 651 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mErr[m[38;2;234;234;234m[48;2;0;0;0m(err) [38;2;129;202;191m=>[m[38;2;234;234;234m[48;2;0;0;0m {[75C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H651,1[9C45%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 652 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mmatch[m[38;2;234;234;234m[48;2;0;0;0m err {[73C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H652,1[9C45%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 653 [m[38;2;234;234;234m[48;2;0;0;0m[20C[38;2;237;158;86mShaderCreationError::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mIo[m[38;2;234;234;234m[48;2;0;0;0m(err) [38;2;129;202;191m=>[m[38;2;234;234;234m[48;2;0;0;0m {[47C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H653,1[9C45%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 654 [m[38;2;234;234;234m[48;2;0;0;0m[24C[38;2;223;101;102merror![m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"Error reading shader file: {}"[m[38;2;234;234;234m[48;2;0;0;0m, err);[31C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H654,1[9C46%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 655 [m[38;2;234;234;234m[48;2;0;0;0m[20C},[78C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H655,1[9C46%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 656 [m[38;2;234;234;234m[48;2;0;0;0m[20C[38;2;237;158;86mShaderCreationError::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mCompile[m[38;2;234;234;234m[48;2;0;0;0m(path, log) [38;2;129;202;191m=>[m[38;2;234;234;234m[48;2;0;0;0m {[36C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H656,1[9C46%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 657 [m[38;2;234;234;234m[48;2;0;0;0m[24C[38;2;223;101;102merror![m[38;2;234;234;234m[48;2;0;0;0m([38;2;197;209;92m"Error compiling shader at {:?}"[m[38;2;234;234;234m[48;2;0;0;0m, path);[29C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H657,1[9C46%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 658 [m[38;2;234;234;234m[48;2;0;0;0m[24C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m _ [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mio::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mcopy[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m log.[38;2;140;182;225mas_bytes[m[38;2;234;234;234m[48;2;0;0;0m(), [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mio::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mstdout[m[38;2;234;234;234m[48;2;0;0;0m());[19C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H658,1[9C46%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 659 [m[38;2;234;234;234m[48;2;0;0;0m[20C}[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H659,1[9C46%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 660 [m[38;2;234;234;234m[48;2;0;0;0m[16C}[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H660,1[9C46%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 661 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H661,0-1[7C46%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 662 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mreturn[m[38;2;234;234;234m[48;2;0;0;0m;[77C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H662,1[9C46%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 663 [m[38;2;234;234;234m[48;2;0;0;0m[12C}[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H663,1[9C46%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 664 [m[38;2;234;234;234m[48;2;0;0;0m[8C};[90C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H664,1[9C46%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 665 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H665,0-1[7C46%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 666 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.active_tex [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m;[72C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H666,1[9C46%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 667 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.program [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m program;[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H667,1[9C47%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 668 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H668,1[9C47%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 669 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H669,0-1[7C47%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 670 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mresize[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, width: [38;2;207;171;224mi32[m[38;2;234;234;234m[48;2;0;0;0m, height: [38;2;207;171;224mi32[m[38;2;234;234;234m[48;2;0;0;0m) {[45C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H670,1[9C47%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 671 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m padding_x [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.program.padding_x [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mi32[m[38;2;234;234;234m[48;2;0;0;0m;[46C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H671,1[9C47%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 672 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m padding_y [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.program.padding_y [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mi32[m[38;2;234;234;234m[48;2;0;0;0m;[46C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H672,1[9C47%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 673 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H673,0-1[7C47%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 674 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;118;120;118m// viewport[m[38;2;234;234;234m[48;2;0;0;0m[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H674,1[9C47%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 675 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224munsafe[m[38;2;234;234;234m[48;2;0;0;0m {[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H675,1[9C47%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 676 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mViewport[m[38;2;234;234;234m[48;2;0;0;0m(padding_x, padding_y, width [38;2;129;202;191m-[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m2[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m padding_x, height [38;2;129;202;191m-[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m2[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m padding_y);      [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H676,1[9C47%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 677 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H677,1[9C47%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 678 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H678,0-1[7C47%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 679 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;118;120;118m// update projection[m[38;2;234;234;234m[48;2;0;0;0m[72C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H679,1[9C47%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 680 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.program.[38;2;140;182;225mactivate[m[38;2;234;234;234m[48;2;0;0;0m();[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H680,1[9C48%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 681 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.program.[38;2;140;182;225mupdate_projection[m[38;2;234;234;234m[48;2;0;0;0m(width [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m, height [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m);[32C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H681,1[9C48%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 682 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.program.[38;2;140;182;225mdeactivate[m[38;2;234;234;234m[48;2;0;0;0m();[66C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H682,1[9C48%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 683 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H683,1[9C48%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 684 [m[38;2;234;234;234m[48;2;0;0;0m}[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H684,1[9C48%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 685 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H685,0-1[7C48%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 686 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mimpl[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m RenderApi[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m {[76C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H686,1[9C48%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 687 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mclear[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;0;0;0m[48;2;236;206;88mcolor[m[38;2;234;234;234m[48;2;0;0;0m: Rgb) {[63C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H687,1[9C48%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 688 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224munsafe[m[38;2;234;234;234m[48;2;0;0;0m {[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H688,1[9C48%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 689 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mClear[m[38;2;234;234;234m[48;2;0;0;0m[38;2;0;0;0m[48;2;236;206;88mColor[m[38;2;234;234;234m[48;2;0;0;0m([73C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H689,1[9C48%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 690 [m[38;2;234;234;234m[48;2;0;0;0m[16C([38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.visual_bell_intensity [38;2;129;202;191m+[m[38;2;234;234;234m[48;2;0;0;0m [38;2;0;0;0m[48;2;236;206;88mcolor[m[38;2;234;234;234m[48;2;0;0;0m.r [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m/[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m255.0[m[38;2;234;234;234m[48;2;0;0;0m).[38;2;140;182;225mmin[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m1.0[m[38;2;234;234;234m[48;2;0;0;0m),[21C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H690,1[9C48%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 691 [m[38;2;234;234;234m[48;2;0;0;0m[16C([38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.visual_bell_intensity [38;2;129;202;191m+[m[38;2;234;234;234m[48;2;0;0;0m [38;2;0;0;0m[48;2;236;206;88mcolor[m[38;2;234;234;234m[48;2;0;0;0m.g [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m/[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m255.0[m[38;2;234;234;234m[48;2;0;0;0m).[38;2;140;182;225mmin[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m1.0[m[38;2;234;234;234m[48;2;0;0;0m),[21C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H691,1[9C48%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 692 [m[38;2;234;234;234m[48;2;0;0;0m[16C([38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.visual_bell_intensity [38;2;129;202;191m+[m[38;2;234;234;234m[48;2;0;0;0m [38;2;0;0;0m[48;2;236;206;88mcolor[m[38;2;234;234;234m[48;2;0;0;0m.b [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mf32[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m/[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m255.0[m[38;2;234;234;234m[48;2;0;0;0m).[38;2;140;182;225mmin[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m1.0[m[38;2;234;234;234m[48;2;0;0;0m),[21C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H692,1[9C48%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 693 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;237;158;86m1.0[m[38;2;234;234;234m[48;2;0;0;0m[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H693,1[9C49%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 694 [m[38;2;234;234;234m[48;2;0;0;0m[16C);[82C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H694,1[9C49%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 695 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mClear[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;0;0;0m[48;2;236;206;88mCOLOR[m[38;2;234;234;234m[48;2;0;0;0m_BUFFER_BIT);[56C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H695,1[9C49%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 696 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H696,1[9C49%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 697 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H697,1[9C49%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 698 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H698,0-1[7C49%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 699 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mrender_batch[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m) {[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H699,1[9C49%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 700 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224munsafe[m[38;2;234;234;234m[48;2;0;0;0m {[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H700,1[9C49%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 701 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBufferSubData[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mARRAY_BUFFER, [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.batch.[38;2;140;182;225msize[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224misize[m[38;2;234;234;234m[48;2;0;0;0m,[22C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H701,1[9C49%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 702 [m[38;2;234;234;234m[48;2;0;0;0m[30C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.batch.instances.[38;2;140;182;225mas_ptr[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mconst[m[38;2;234;234;234m[48;2;0;0;0m _);[27C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H702,1[9C49%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 703 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H703,1[9C49%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 704 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H704,0-1[7C49%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 705 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;118;120;118m// Bind texture if necessary[m[38;2;234;234;234m[48;2;0;0;0m[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H705,1[9C50%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 706 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.active_tex [38;2;129;202;191m!=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.batch.tex {[53C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H706,1[9C50%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 707 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224munsafe[m[38;2;234;234;234m[48;2;0;0;0m {[80C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H707,1[9C50%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 708 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mBindTexture[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mTEXTURE_2D, [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.batch.tex);[36C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H708,1[9C50%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 709 [m[38;2;234;234;234m[48;2;0;0;0m[12C}[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H709,1[9C50%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 710 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.active_tex [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.batch.tex;[54C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H710,1[9C50%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 711 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H711,1[9C50%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 712 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H712,0-1[7C50%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 713 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224munsafe[m[38;2;234;234;234m[48;2;0;0;0m {[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H713,1[9C50%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 714 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.program.[38;2;140;182;225mset_background_pass[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mtrue[m[38;2;234;234;234m[48;2;0;0;0m);[49C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H714,1[9C50%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 715 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mDrawElementsInstanced[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mTRIANGLES,[48C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H715,1[9C50%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 716 [m[38;2;234;234;234m[48;2;0;0;0m[38C[38;2;237;158;86m6[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mUNSIGNED_INT, [38;2;237;158;86mptr::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mnull[m[38;2;234;234;234m[48;2;0;0;0m(),[29C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H716,1[9C50%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 717 [m[38;2;234;234;234m[48;2;0;0;0m[38C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.batch.[38;2;140;182;225mlen[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m GLsizei);[33C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H717,1[9C50%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 718 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.program.[38;2;140;182;225mset_background_pass[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mfalse[m[38;2;234;234;234m[48;2;0;0;0m);[48C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H718,1[9C51%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 719 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mDrawElementsInstanced[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mTRIANGLES,[48C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H719,1[9C51%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 720 [m[38;2;234;234;234m[48;2;0;0;0m[38C[38;2;237;158;86m6[m[38;2;234;234;234m[48;2;0;0;0m, [38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mUNSIGNED_INT, [38;2;237;158;86mptr::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mnull[m[38;2;234;234;234m[48;2;0;0;0m(),[29C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H720,1[9C51%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 721 [m[38;2;234;234;234m[48;2;0;0;0m[38C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.batch.[38;2;140;182;225mlen[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191mas[m[38;2;234;234;234m[48;2;0;0;0m GLsizei);[33C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H721,1[9C51%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 722 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H722,1[9C51%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 723 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H723,0-1[7C51%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 724 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.batch.[38;2;140;182;225mclear[m[38;2;234;234;234m[48;2;0;0;0m();[73C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H724,1[9C51%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 725 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H725,1[9C51%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 726 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// Render a string in a predefined location. Used for printing render time for profiling and[m[38;2;234;234;234m[48;2;0;0;0m   [48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H726,1[9C51%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 727 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// optimization.[m[38;2;234;234;234m[48;2;0;0;0m[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H727,1[9C51%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 728 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mrender_string[m[38;2;234;234;234m[48;2;0;0;0m([75C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H728,1[9C51%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 729 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m,[82C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H729,1[9C51%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 730 [m[38;2;234;234;234m[48;2;0;0;0m[8Cstring: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mstr[m[38;2;234;234;234m[48;2;0;0;0m,[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H730,1[9C51%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 731 [m[38;2;234;234;234m[48;2;0;0;0m[8Cglyph_cache: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m GlyphCache,[63C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H731,1[9C52%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 732 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;0;0;0m[48;2;236;206;88mcolor[m[38;2;234;234;234m[48;2;0;0;0m: Rgb,[81C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H732,1[9C52%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 733 [m[38;2;234;234;234m[48;2;0;0;0m    ) {[93C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H733,1[9C52%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 734 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m line [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mLine[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m23[m[38;2;234;234;234m[48;2;0;0;0m);[72C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H734,1[9C52%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 735 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m col [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mColumn[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m);[72C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H735,1[9C52%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 736 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H736,0-1[7C52%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 737 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m cells [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m string.[38;2;140;182;225mchars[m[38;2;234;234;234m[48;2;0;0;0m()[66C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H737,1[9C52%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 738 [m[38;2;234;234;234m[48;2;0;0;0m[12C.[38;2;140;182;225menumerate[m[38;2;234;234;234m[48;2;0;0;0m()[76C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H738,1[9C52%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 739 [m[38;2;234;234;234m[48;2;0;0;0m[12C.[38;2;140;182;225mmap[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m|[m[38;2;234;234;234m[48;2;0;0;0m(i, c)[38;2;129;202;191m|[m[38;2;234;234;234m[48;2;0;0;0m RenderableCell {[58C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H739,1[9C52%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 740 [m[38;2;234;234;234m[48;2;0;0;0m[16Cline: line,[73C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H740,1[9C52%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 741 [m[38;2;234;234;234m[48;2;0;0;0m[16Ccolumn: col [38;2;129;202;191m+[m[38;2;234;234;234m[48;2;0;0;0m i,[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H741,1[9C52%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 742 [m[38;2;234;234;234m[48;2;0;0;0m[16Cc: c,[79C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H742,1[9C52%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 743 [m[38;2;234;234;234m[48;2;0;0;0m[16Cbg: [38;2;0;0;0m[48;2;236;206;88mcolor[m[38;2;234;234;234m[48;2;0;0;0m,[74C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H743,1[9C52%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 744 [m[38;2;234;234;234m[48;2;0;0;0m[16Cfg: Rgb { r: [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m, g: [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m, b: [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m },[55C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H744,1[9C53%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 745 [m[38;2;234;234;234m[48;2;0;0;0m[16Cflags: [38;2;237;158;86mcell::Flags::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mempty[m[38;2;234;234;234m[48;2;0;0;0m(),[56C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H745,1[9C53%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 746 [m[38;2;234;234;234m[48;2;0;0;0m[12C})[86C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H746,1[9C53%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 747 [m[38;2;234;234;234m[48;2;0;0;0m[12C.[38;2;140;182;225mcollect[m[38;2;234;234;234m[48;2;0;0;0m[38;2;237;158;86m::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mVec[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m_[38;2;129;202;191m>>[m[38;2;234;234;234m[48;2;0;0;0m();[67C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H747,1[9C53%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 748 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H748,0-1[7C53%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 749 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.[38;2;140;182;225mrender_cells[m[38;2;234;234;234m[48;2;0;0;0m(cells.[38;2;140;182;225minto_iter[m[38;2;234;234;234m[48;2;0;0;0m(), glyph_cache);[42C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H749,1[9C53%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 750 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H750,1[9C53%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 751 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H751,0-1[7C53%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 752 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;223;101;102m#[inline][m[38;2;234;234;234m[48;2;0;0;0m[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H752,1[9C53%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 753 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225madd_render_item[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, cell: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mRenderableCell, glyph: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mGlyph) {[27C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H753,1[9C53%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 754 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;118;120;118m// Flush batch if tex changing[m[38;2;234;234;234m[48;2;0;0;0m[62C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H754,1[9C53%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 755 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m![m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.batch.[38;2;140;182;225mis_empty[m[38;2;234;234;234m[48;2;0;0;0m() [38;2;129;202;191m&&[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.batch.tex [38;2;129;202;191m!=[m[38;2;234;234;234m[48;2;0;0;0m glyph.tex_id {[31C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H755,1[9C53%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 756 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.[38;2;140;182;225mrender_batch[m[38;2;234;234;234m[48;2;0;0;0m();[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H756,1[9C53%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 757 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H757,1[9C54%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 758 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H758,0-1[7C54%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 759 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.batch.[38;2;140;182;225madd_item[m[38;2;234;234;234m[48;2;0;0;0m(cell, glyph);[59C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H759,1[9C54%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 760 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H760,0-1[7C54%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 761 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;118;120;118m// Render batch and clear if it's full[m[38;2;234;234;234m[48;2;0;0;0m[54C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H761,1[9C54%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 762 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.batch.[38;2;140;182;225mfull[m[38;2;234;234;234m[48;2;0;0;0m() {[70C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H762,1[9C54%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 763 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.[38;2;140;182;225mrender_batch[m[38;2;234;234;234m[48;2;0;0;0m();[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H763,1[9C54%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 764 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H764,1[9C54%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 765 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H765,1[9C54%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 766 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H766,0-1[7C54%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 767 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mrender_cells[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mI[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m([73C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H767,1[9C54%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 768 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m,[82C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H768,1[9C54%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 769 [m[38;2;234;234;234m[48;2;0;0;0m[8Ccells: I,[83C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H769,1[9C54%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 770 [m[38;2;234;234;234m[48;2;0;0;0m[8Cglyph_cache: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m GlyphCache[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H770,1[9C55%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 771 [m[38;2;234;234;234m[48;2;0;0;0m    )[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H771,1[9C55%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 772 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mwhere[m[38;2;234;234;234m[48;2;0;0;0m I: [38;2;207;171;224mIterator[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mItem[38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0mRenderableCell[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m[54C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H772,1[9C55%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 773 [m[38;2;234;234;234m[48;2;0;0;0m    {[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H773,1[9C55%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 774 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mfor[m[38;2;234;234;234m[48;2;0;0;0m cell [38;2;207;171;224min[m[38;2;234;234;234m[48;2;0;0;0m cells {[73C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H774,1[9C55%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 775 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// Get font key for cell[m[38;2;234;234;234m[48;2;0;0;0m[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H775,1[9C55%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 776 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// [m[38;2;234;234;234m[48;2;0;0;0m[1m[38;2;236;206;88mFIXME[m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m this is super inefficient.[m[38;2;234;234;234m[48;2;0;0;0m[53C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H776,1[9C55%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 777 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m font_key [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m glyph_cache.font_key;[48C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H777,1[9C55%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 778 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m cell.flags.[38;2;140;182;225mcontains[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mcell::[m[38;2;234;234;234m[48;2;0;0;0mBOLD) {[52C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H778,1[9C55%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 779 [m[38;2;234;234;234m[48;2;0;0;0m[16Cfont_key [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m glyph_cache.bold_key;[52C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H779,1[9C55%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 780 [m[38;2;234;234;234m[48;2;0;0;0m[12C} [38;2;207;171;224melse[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m cell.flags.[38;2;140;182;225mcontains[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mcell::[m[38;2;234;234;234m[48;2;0;0;0mITALIC) {[43C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H780,1[9C55%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 781 [m[38;2;234;234;234m[48;2;0;0;0m[16Cfont_key [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m glyph_cache.italic_key;[50C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H781,1[9C55%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 782 [m[38;2;234;234;234m[48;2;0;0;0m[12C}[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H782,1[9C55%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 783 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H783,0-1[7C56%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 784 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m glyph_key [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m GlyphKey {[62C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H784,1[9C56%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 785 [m[38;2;234;234;234m[48;2;0;0;0m[16Cfont_key: font_key,[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H785,1[9C56%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 786 [m[38;2;234;234;234m[48;2;0;0;0m[16Csize: glyph_cache.font_size,[56C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H786,1[9C56%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 787 [m[38;2;234;234;234m[48;2;0;0;0m[16Cc: cell.c[75C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H787,1[9C56%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 788 [m[38;2;234;234;234m[48;2;0;0;0m[12C};[86C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H788,1[9C56%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 789 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H789,0-1[7C56%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 790 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// Add cell to batch[m[38;2;234;234;234m[48;2;0;0;0m[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H790,1[9C56%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 791 [m[38;2;234;234;234m[48;2;0;0;0m[12C{[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H791,1[9C56%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 792 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m glyph [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m glyph_cache.[38;2;140;182;225mget[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mglyph_key, [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m);[38C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H792,1[9C56%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 793 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.[38;2;140;182;225madd_render_item[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mcell, glyph);[49C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H793,1[9C56%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 794 [m[38;2;234;234;234m[48;2;0;0;0m[12C}[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H794,1[9C56%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 795 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H795,0-1[7C56%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 796 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m// [m[38;2;234;234;234m[48;2;0;0;0m[1m[38;2;236;206;88mFIXME[m[38;2;234;234;234m[48;2;0;0;0m[38;2;118;120;118m This is a super hacky way to do underlined text. During[m[38;2;234;234;234m[48;2;0;0;0m[24C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H796,1[9C57%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 797 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m//       a time crunch to release 0.1, this seemed like a really[m[38;2;234;234;234m[48;2;0;0;0m[24C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H797,1[9C57%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 798 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;118;120;118m//       easy, clean hack.[m[38;2;234;234;234m[48;2;0;0;0m[62C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H798,1[9C57%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 799 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m cell.flags.[38;2;140;182;225mcontains[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86mcell::[m[38;2;234;234;234m[48;2;0;0;0mUNDERLINE) {[47C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H799,1[9C57%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 800 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m glyph_key [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m GlyphKey {[58C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H800,1[9C57%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 801 [m[38;2;234;234;234m[48;2;0;0;0m[20Cfont_key: font_key,[61C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H801,1[9C57%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 802 [m[38;2;234;234;234m[48;2;0;0;0m[20Csize: glyph_cache.font_size,[52C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H802,1[9C57%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 803 [m[38;2;234;234;234m[48;2;0;0;0m[20Cc: [38;2;237;158;86m'_'[m[38;2;234;234;234m[48;2;0;0;0m[74C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H803,1[9C57%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 804 [m[38;2;234;234;234m[48;2;0;0;0m[16C};[82C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H804,1[9C57%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 805 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H805,0-1[7C57%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 806 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m underscore [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m glyph_cache.[38;2;140;182;225mget[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mglyph_key, [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m);[33C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H806,1[9C57%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 807 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.[38;2;140;182;225madd_render_item[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mcell, underscore);[44C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H807,1[9C57%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 808 [m[38;2;234;234;234m[48;2;0;0;0m[12C}[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H808,1[9C57%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 809 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H809,1[9C58%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 810 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H810,1[9C58%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 811 [m[38;2;234;234;234m[48;2;0;0;0m}[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H811,1[9C58%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 812 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H812,0-1[7C58%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 813 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mimpl[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m LoadGlyph [38;2;207;171;224mfor[m[38;2;234;234;234m[48;2;0;0;0m LoaderApi[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m {[62C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H813,1[9C58%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 814 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// Load a glyph into a texture atlas[m[38;2;234;234;234m[48;2;0;0;0m[59C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H814,1[9C58%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 815 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m///[m[38;2;234;234;234m[48;2;0;0;0m[93C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H815,1[9C58%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 816 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// If the current atlas is full, a new one will be created.[m[38;2;234;234;234m[48;2;0;0;0m[36C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H816,1[9C58%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 817 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mload_glyph[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, rasterized: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mRasterizedGlyph) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m Glyph {[31C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H817,1[9C58%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 818 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;118;120;118m// At least one atlas is guaranteed to be in the `self.atlas` list; thus[m[38;2;234;234;234m[48;2;0;0;0m[20C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H818,1[9C58%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 819 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;118;120;118m// the unwrap should always be ok.[m[38;2;234;234;234m[48;2;0;0;0m[58C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H819,1[9C58%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 820 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mmatch[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.atlas.[38;2;140;182;225mlast_mut[m[38;2;234;234;234m[48;2;0;0;0m().[38;2;140;182;225munwrap[m[38;2;234;234;234m[48;2;0;0;0m().[38;2;140;182;225minsert[m[38;2;234;234;234m[48;2;0;0;0m(rasterized, [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.active_tex) {[13C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H820,1[9C58%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 821 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mOk[m[38;2;234;234;234m[48;2;0;0;0m(glyph) [38;2;129;202;191m=>[m[38;2;234;234;234m[48;2;0;0;0m glyph,[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H821,1[9C58%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 822 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mErr[m[38;2;234;234;234m[48;2;0;0;0m(_) [38;2;129;202;191m=>[m[38;2;234;234;234m[48;2;0;0;0m {[77C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H822,1[9C59%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 823 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m atlas [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mAtlas::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mnew[m[38;2;234;234;234m[48;2;0;0;0m(ATLAS_SIZE);[49C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H823,1[9C59%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 824 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.active_tex [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m; [38;2;118;120;118m// Atlas::new binds a texture. Ugh this is sloppy.[m[38;2;234;234;234m[48;2;0;0;0m[12C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H824,1[9C59%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 825 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.atlas.[38;2;140;182;225mpush[m[38;2;234;234;234m[48;2;0;0;0m(atlas);[61C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H825,1[9C59%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 826 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.[38;2;140;182;225mload_glyph[m[38;2;234;234;234m[48;2;0;0;0m(rasterized)[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H826,1[9C59%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 827 [m[38;2;234;234;234m[48;2;0;0;0m[12C}[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H827,1[9C59%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 828 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H828,1[9C59%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 829 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H829,1[9C59%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[39;43H[48;2;66;66;66m{[m[38;2;234;234;234m[48;2;0;0;0m[56;1H[38;2;66;66;66m 830 [m[38;2;234;234;234m[48;2;0;0;0m[48;2;66;66;66m}[m[38;2;234;234;234m[48;2;0;0;0m[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H830,1[9C59%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[38;43H{[55;6H}
[38;2;66;66;66m 831 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H831,0-1[7C59%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 832 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mimpl[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m LoadGlyph [38;2;207;171;224mfor[m[38;2;234;234;234m[48;2;0;0;0m RenderApi[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m {[62C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H832,1[9C59%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 833 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// Load a glyph into a texture atlas[m[38;2;234;234;234m[48;2;0;0;0m[59C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H833,1[9C59%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 834 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m///[m[38;2;234;234;234m[48;2;0;0;0m[93C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H834,1[9C59%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 835 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;118;120;118m/// If the current atlas is full, a new one will be created.[m[38;2;234;234;234m[48;2;0;0;0m[36C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H835,1[9C60%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 836 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mload_glyph[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m, rasterized: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mRasterizedGlyph) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m Glyph {[31C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H836,1[9C60%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 837 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;118;120;118m// At least one atlas is guaranteed to be in the `self.atlas` list; thus[m[38;2;234;234;234m[48;2;0;0;0m[20C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H837,1[9C60%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 838 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;118;120;118m// the unwrap.[m[38;2;234;234;234m[48;2;0;0;0m[78C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H838,1[9C60%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 839 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mmatch[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.atlas.[38;2;140;182;225mlast_mut[m[38;2;234;234;234m[48;2;0;0;0m().[38;2;140;182;225munwrap[m[38;2;234;234;234m[48;2;0;0;0m().[38;2;140;182;225minsert[m[38;2;234;234;234m[48;2;0;0;0m(rasterized, [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.active_tex) {[13C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H839,1[9C60%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 840 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mOk[m[38;2;234;234;234m[48;2;0;0;0m(glyph) [38;2;129;202;191m=>[m[38;2;234;234;234m[48;2;0;0;0m glyph,[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H840,1[9C60%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 841 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mErr[m[38;2;234;234;234m[48;2;0;0;0m(_) [38;2;129;202;191m=>[m[38;2;234;234;234m[48;2;0;0;0m {[77C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H841,1[9C60%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 842 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m atlas [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mAtlas::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mnew[m[38;2;234;234;234m[48;2;0;0;0m(ATLAS_SIZE);[49C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H842,1[9C60%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 843 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;129;202;191m*[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.active_tex [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m; [38;2;118;120;118m// Atlas::new binds a texture. Ugh this is sloppy.[m[38;2;234;234;234m[48;2;0;0;0m[12C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H843,1[9C60%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 844 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.atlas.[38;2;140;182;225mpush[m[38;2;234;234;234m[48;2;0;0;0m(atlas);[61C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H844,1[9C60%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 845 [m[38;2;234;234;234m[48;2;0;0;0m[16C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.[38;2;140;182;225mload_glyph[m[38;2;234;234;234m[48;2;0;0;0m(rasterized)[57C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H845,1[9C60%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 846 [m[38;2;234;234;234m[48;2;0;0;0m[12C}[87C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H846,1[9C60%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 847 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H847,1[9C60%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 848 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H848,1[9C61%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[39;43H[48;2;66;66;66m{[m[38;2;234;234;234m[48;2;0;0;0m[56;1H[38;2;66;66;66m 849 [m[38;2;234;234;234m[48;2;0;0;0m[48;2;66;66;66m}[m[38;2;234;234;234m[48;2;0;0;0m[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H849,1[9C61%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[38;43H{[55;6H}
[38;2;66;66;66m 850 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H850,0-1[7C61%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 851 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mimpl[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mDrop[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfor[m[38;2;234;234;234m[48;2;0;0;0m RenderApi[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[3m[38;2;223;101;102m'a[23m[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m {[67C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H851,1[9C61%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 852 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mdrop[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mmut[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m) {[76C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H852,1[9C61%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 853 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m [38;2;129;202;191m![m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.batch.[38;2;140;182;225mis_empty[m[38;2;234;234;234m[48;2;0;0;0m() {[65C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H853,1[9C61%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 854 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.[38;2;140;182;225mrender_batch[m[38;2;234;234;234m[48;2;0;0;0m();[68C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H854,1[9C61%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 855 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H855,1[9C61%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 856 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H856,1[9C61%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[50;38H[48;2;66;66;66m{[m[38;2;234;234;234m[48;2;0;0;0m[56;1H[38;2;66;66;66m 857 [m[38;2;234;234;234m[48;2;0;0;0m[48;2;66;66;66m}[m[38;2;234;234;234m[48;2;0;0;0m[99C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H857,1[9C61%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[49;38H{[55;6H}
[38;2;66;66;66m 858 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H858,0-1[7C61%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 859 [m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mimpl[m[38;2;234;234;234m[48;2;0;0;0m ShaderProgram {[80C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H859,1[9C61%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 860 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mactivate[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m) {[72C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H860,1[9C61%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 861 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224munsafe[m[38;2;234;234;234m[48;2;0;0;0m {[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H861,1[9C62%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 862 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mUseProgram[m[38;2;234;234;234m[48;2;0;0;0m([38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m.id);[64C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H862,1[9C62%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 863 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H863,1[9C62%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 864 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H864,1[9C62%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 865 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H865,0-1[7C62%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 866 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mdeactivate[m[38;2;234;234;234m[48;2;0;0;0m([38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mself[m[38;2;234;234;234m[48;2;0;0;0m) {[70C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H866,1[9C62%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 867 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224munsafe[m[38;2;234;234;234m[48;2;0;0;0m {[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H867,1[9C62%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 868 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mUseProgram[m[38;2;234;234;234m[48;2;0;0;0m([38;2;237;158;86m0[m[38;2;234;234;234m[48;2;0;0;0m);[70C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H868,1[9C62%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 869 [m[38;2;234;234;234m[48;2;0;0;0m[8C}[91C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H869,1[9C62%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 870 [m[38;2;234;234;234m[48;2;0;0;0m    }[95C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H870,1[9C62%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 871 [m[38;2;234;234;234m[48;2;0;0;0m[100C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H871,0-1[7C62%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 872 [m[38;2;234;234;234m[48;2;0;0;0m    [38;2;207;171;224mpub[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mfn[m[38;2;234;234;234m[48;2;0;0;0m [38;2;140;182;225mnew[m[38;2;234;234;234m[48;2;0;0;0m([85C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H872,1[9C62%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 873 [m[38;2;234;234;234m[48;2;0;0;0m[8Cconfig: [38;2;129;202;191m&[m[38;2;234;234;234m[48;2;0;0;0mConfig,[76C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H873,1[9C62%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 874 [m[38;2;234;234;234m[48;2;0;0;0m[8Csize: Size[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mPixels[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0m[38;2;207;171;224mu32[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m>>[m[38;2;234;234;234m[48;2;0;0;0m[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H874,1[9C63%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 875 [m[38;2;234;234;234m[48;2;0;0;0m    ) [38;2;129;202;191m->[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mResult[m[38;2;234;234;234m[48;2;0;0;0m[38;2;129;202;191m<[m[38;2;234;234;234m[48;2;0;0;0mShaderProgram, ShaderCreationError[38;2;129;202;191m>[m[38;2;234;234;234m[48;2;0;0;0m {[47C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H875,1[9C63%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 876 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m vertex_source [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m [38;2;223;101;102mcfg![m[38;2;234;234;234m[48;2;0;0;0m(feature [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;197;209;92m"live-shader-reload"[m[38;2;234;234;234m[48;2;0;0;0m) {[31C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H876,1[9C63%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 877 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mNone[m[38;2;234;234;234m[48;2;0;0;0m[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H877,1[9C63%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 878 [m[38;2;234;234;234m[48;2;0;0;0m[8C} [38;2;207;171;224melse[m[38;2;234;234;234m[48;2;0;0;0m {[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H878,1[9C63%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 879 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mSome[m[38;2;234;234;234m[48;2;0;0;0m(TEXT_SHADER_V)[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H879,1[9C63%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 880 [m[38;2;234;234;234m[48;2;0;0;0m[8C};[90C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H880,1[9C63%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 881 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m vertex_shader [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mShaderProgram::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mcreate_shader[m[38;2;234;234;234m[48;2;0;0;0m([43C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H881,1[9C63%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 882 [m[38;2;234;234;234m[48;2;0;0;0m[12CTEXT_SHADER_V_PATH,[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H882,1[9C63%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 883 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mgl::[m[38;2;234;234;234m[48;2;0;0;0mVERTEX_SHADER,[70C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H883,1[9C63%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 884 [m[38;2;234;234;234m[48;2;0;0;0m[12Cvertex_source[75C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H884,1[9C63%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 885 [m[38;2;234;234;234m[48;2;0;0;0m[8C)[38;2;129;202;191m?[m[38;2;234;234;234m[48;2;0;0;0m;[89C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H885,1[9C63%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 886 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m frag_source [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;207;171;224mif[m[38;2;234;234;234m[48;2;0;0;0m [38;2;223;101;102mcfg![m[38;2;234;234;234m[48;2;0;0;0m(feature [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;197;209;92m"live-shader-reload"[m[38;2;234;234;234m[48;2;0;0;0m) {[33C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H886,1[9C63%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 887 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mNone[m[38;2;234;234;234m[48;2;0;0;0m[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H887,1[9C64%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 888 [m[38;2;234;234;234m[48;2;0;0;0m[8C} [38;2;207;171;224melse[m[38;2;234;234;234m[48;2;0;0;0m {[84C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H888,1[9C64%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 889 [m[38;2;234;234;234m[48;2;0;0;0m[12C[38;2;237;158;86mSome[m[38;2;234;234;234m[48;2;0;0;0m(TEXT_SHADER_F)[69C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H889,1[9C64%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 890 [m[38;2;234;234;234m[48;2;0;0;0m[8C};[90C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H890,1[9C64%[56;6H[?12l[?25h[?25l[1;56r[56;1H
[1;57r[56;1H[38;2;66;66;66m 891 [m[38;2;234;234;234m[48;2;0;0;0m[8C[38;2;207;171;224mlet[m[38;2;234;234;234m[48;2;0;0;0m fragment_shader [38;2;129;202;191m=[m[38;2;234;234;234m[48;2;0;0;0m [38;2;237;158;86mShaderProgram::[m[38;2;234;234;234m[48;2;0;0;0m[38;2;140;182;225mcreate_shader[m[38;2;234;234;234m[48;2;0;0;0m([41C[48;2;42;42;42m [m[38;2;234;234;234m[48;2;0;0;0m[57;103H[K[57;103H891,1[9C64%[56;6H[?12l[?25h