[?2004h[k<PERSON><PERSON><PERSON>@NightLord alacritty]$ 
[K[k<PERSON><PERSON><PERSON>@NightLord alacritty]$ 
[K[k<PERSON><PERSON><PERSON>@NightLord alacritty]$ cargo [K[K[K[K[K[Kcat ../alacritty.recording 
[?2004l
[?2004h[k<PERSON><PERSON><PERSON>@NightLord forks]$ 
[K[k<PERSON><PERSON><PERSON>@NightLord forks]$ 
[K[kchi<PERSON><PERSON>@NightLord forks]$ prin[K[K[K[Kcd Developer/rust/forks/alacritty
[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[Chtop[Kprintf "\e[31;1;7;4;9mTEST asd\n"

[?2004l
[31;1;7;4;9mTEST asd

[?2004h[kchibisov@NightLord forks]$ [H[2J[kchibisov@NightLord forks]$ htop

[?2004l
[?1049h[22;0;0t[1;30r(B[m[4l[?7h[?1h=[?25l[39;49m[?1000h[39;49m(B[m[H[2J[2d  [36m1  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m5  [39m(B[0;1m[[90m             0.0%[39m](B[m   [36m9  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m13 [39m(B[0;1m[[90m             0.0%[39m][3;3H(B[0m[36m2  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m6  [39m(B[0;1m[[90m             0.0%[39m](B[m   [36m10 [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m14 [39m(B[0;1m[[90m             0.0%[39m][4;3H(B[0m[36m3  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m7  [39m(B[0;1m[(B[0m[31m|||||||||||100.0%[39m(B[0;1m](B[m   [36m11 [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m15 [39m(B[0;1m[[90m             0.0%[39m][5;3H(B[0m[36m4  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m8  [39m(B[0;1m[[90m             0.0%[39m](B[m   [36m12 [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m16 [39m(B[0;1m[[90m             0.0%[39m][6;3H(B[0m[36mMem[39m(B[0;1m[(B[0m[32m|||||[34m|[33m|||||||||(B[0;1m[90m[17X[6;39H3.33G/31.3G[39m](B[m   [36mTasks: (B[0;1m[36m66(B[0m[36m; (B[0;1m[32m1(B[0m[36m running[7;3HSwp[39m(B[0;1m[[90m[38X[7;45H0K/0K[39m](B[m   [36mLoad average: [39m(B[0;1m0.65 [36m0.81 (B[0m[36m0.93 [8;54HUptime: (B[0;1m[36m06:03:58
[10d(B[0m[30m[42m    PID USER      PRI  NI  VIRT   RES   SHR S [30m[46mCPU% [30m[42mMEM%   TIME+  Command[K
[11d[30m[46m  14237 kchibisov  20   0 4259M  564M  276M S 100.  1.8  1h00:58 /usr/lib/firefox/firefox[K[12;2H[39;49m(B[m170449 kchibisov  20   0 [36m 8[39m(B[m664 [36m 4[39m(B[m572 [36m 3[39m(B[m312 [32mR [39m(B[m100.  0.0  0:00.03 (B[0;1m[36mhtop[13;7H[39m(B[m1 (B[0;1m[90mroot      [39m(B[m 20   0 [36m 183M 10[39m(B[m388 [36m 7[39m(B[m868 S  0.0  0.0  0:22.33 /sbin/(B[0;1m[36minit[14;5H[39m(B[m421 (B[0;1m[90mroot      [39m(B[m 20   0 [36m61[39m(B[m176 [36m18[39m(B[m424 [36m17[39m(B[m320 S  0.0  0.1  0:00.60 /usr/lib/systemd/(B[0;1m[36msystemd-journald[15;5H[39m(B[m430 (B[0;1m[90mroot      [39m(B[m 20   0 [36m 148M  1[39m(B[m508 [36m 1[39m(B[m280 S  0.0  0.0  0:00.00 /usr/bin/(B[0;1m[36mlvmetad[39m(B[m -f[16;5H436 (B[0;1m[90mroot      [39m(B[m 20   0 [36m40[39m(B[m492 [36m 9[39m(B[m048 [36m 6[39m(B[m648 S  0.0  0.0  0:00.42 /usr/lib/systemd/(B[0;1m[36msystemd-udevd[17;5H[39m(B[m587 (B[0;1m[90msystemd-t [39m(B[m 20   0 [36m  98M  7[39m(B[m308 [36m 6[39m(B[m364 S  0.0  0.0  0:00.16 /usr/lib/systemd/(B[0;1m[36msystemd-timesyncd[18;5H[39m(B[m601 (B[0;1m[90mdbus      [39m(B[m 20   0 [36m10[39m(B[m980 [36m 4[39m(B[m464 [36m 3[39m(B[m596 S  0.0  0.0  0:01.14 /usr/bin/(B[0;1m[36mdbus-daemon[39m(B[m --system --address=s[19;5H602 (B[0;1m[90mroot      [39m(B[m 20   0 [36m 401M 19[39m(B[m084 [36m16[39m(B[m288 S  0.0  0.1  0:00.74 /usr/bin/(B[0;1m[36mNetworkManager[39m(B[m --no-daemon[20;5H606 (B[0;1m[90mroot      [39m(B[m 20   0 [36m12[39m(B[m580 [36m 5[39m(B[m960 [36m 5[39m(B[m212 S  0.0  0.0  0:00.00 /usr/bin/(B[0;1m[36msshd[39m(B[m -D[21;5H609 (B[0;1m[90mroot      [39m(B[m 20   0 [36m43[39m(B[m028 [36m 3[39m(B[m608 [36m 3[39m(B[m040 S  0.0  0.0  0:00.02 (B[0;1m[36mlogin -- kchibisov[22;5H[39m(B[m611 (B[0;1m[90mroot      [39m(B[m 20   0 [36m26[39m(B[m440 [36m 7[39m(B[m508 [36m 6[39m(B[m536 S  0.0  0.0  0:02.89 /usr/lib/systemd/(B[0;1m[36msystemd-logind[23;5H[39m(B[m629 kchibisov  20   0 [36m32[39m(B[m320 [36m 9[39m(B[m536 [36m 7[39m(B[m788 S  0.0  0.0  0:00.25 /usr/lib/systemd/(B[0;1m[36msystemd[39m(B[m --user[24;5H630 kchibisov  20   0 [36m74[39m(B[m288 [36m 2[39m(B[m792    28 S  0.0  0.0  0:00.00 (B[0;1m[36m(sd-pam)[25;5H[39m(B[m636 kchibisov  20   0 [36m 7[39m(B[m592 [36m 4[39m(B[m128 [36m 3[39m(B[m340 S  0.0  0.0  0:00.00 (B[0;1m[36m-bash[26;5H[39m(B[m678 kchibisov  20   0 [36m 7[39m(B[m124 [36m 2[39m(B[m744 [36m 2[39m(B[m400 S  0.0  0.0  0:00.00 /bin/(B[0;1m[36mbash[39m(B[m /usr/local/bin/start-sway[27;5H679 kchibisov  20   0 [36m1919M  253M  225M [39m(B[mS  0.0  0.8  5:08.51 /home/<USER>/Developer/cc/sway-repaint[28;5H702 kchibisov  20   0 [36m83[39m(B[m092 [36m72[39m(B[m800 [36m39[39m(B[m488 S  0.0  0.2  0:00.12 (B[0;1m[36mswaybg[39m(B[m -o * -i /home/<USER>/Pictures/W[29;5H704 kchibisov  20   0 [36m1200M 49[39m(B[m124 [36m40[39m(B[m400 S  0.0  0.1  0:20.01 (B[0;1m[36mwaybar[39m(B[m -b bar-0
[30dF1[30m[46mHelp  [39;49m(B[mF2[30m[46mSetup [39;49m(B[mF3[30m[46mSearch[39;49m(B[mF4[30m[46mFilter[39;49m(B[mF5[30m[46mTree  [39;49m(B[mF6[30m[46mSortBy[39;49m(B[mF7[30m[46mNice -[39;49m(B[mF8[30m[46mNice +[39;49m(B[mF9[30m[46mKill  [39;49m(B[mF10[30m[46mQuit[K[H[39;49m(B[m[30d[J[?12l[?25h[?1000l[30;1H[?1049l[23;0;0t
[?1l>[?2004h[kchibisov@NightLord forks]$ htop

[?2004l
[?1049h[22;0;0t[1;30r(B[m[4l[?7h[?1h=[?25l[39;49m[?1000h[39;49m(B[m[H[2J[2d  [36m1  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m5  [39m(B[0;1m[[90m             0.0%[39m](B[m   [36m9  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m13 [39m(B[0;1m[[90m             0.0%[39m][3;3H(B[0m[36m2  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m6  [39m(B[0;1m[[90m             0.0%[39m](B[m   [36m10 [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m14 [39m(B[0;1m[[90m             0.0%[39m][4;3H(B[0m[36m3  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m7  [39m(B[0;1m[[90m             0.0%[39m](B[m   [36m11 [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m15 [39m(B[0;1m[[90m             0.0%[39m][5;3H(B[0m[36m4  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m8  [39m(B[0;1m[(B[0m[32m|||||||||||100.0%[39m(B[0;1m](B[m   [36m12 [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m16 [39m(B[0;1m[[90m             0.0%[39m][6;3H(B[0m[36mMem[39m(B[0;1m[(B[0m[32m|||||[34m|[33m|||||||||(B[0;1m[90m[17X[6;39H3.33G/31.3G[39m](B[m   [36mTasks: (B[0;1m[36m66(B[0m[36m; (B[0;1m[32m2(B[0m[36m running[7;3HSwp[39m(B[0;1m[[90m[38X[7;45H0K/0K[39m](B[m   [36mLoad average: [39m(B[0;1m0.65 [36m0.81 (B[0m[36m0.93 [8;54HUptime: (B[0;1m[36m06:03:59
[10d(B[0m[30m[42m    PID USER      PRI  NI  VIRT   RES   SHR S [30m[46mCPU% [30m[42mMEM%   TIME+  Command[K
[11d[30m[46m 170459 kchibisov  20   0  8664  4592  3332 R 200.  0.0  0:00.03 htop[K[12;2H[39;49m(B[m143146 kchibisov  20   0 [36m9267M  547M  176M [32mR [39m(B[m100.  1.7 27:34.90 /usr/lib/firefox/(B[0;1m[36mfirefox[39m(B[m -contentproc -ch[13;7H1 (B[0;1m[90mroot      [39m(B[m 20   0 [36m 183M 10[39m(B[m388 [36m 7[39m(B[m868 S  0.0  0.0  0:22.33 /sbin/(B[0;1m[36minit[14;5H[39m(B[m421 (B[0;1m[90mroot      [39m(B[m 20   0 [36m61[39m(B[m176 [36m18[39m(B[m424 [36m17[39m(B[m320 S  0.0  0.1  0:00.60 /usr/lib/systemd/(B[0;1m[36msystemd-journald[15;5H[39m(B[m430 (B[0;1m[90mroot      [39m(B[m 20   0 [36m 148M  1[39m(B[m508 [36m 1[39m(B[m280 S  0.0  0.0  0:00.00 /usr/bin/(B[0;1m[36mlvmetad[39m(B[m -f[16;5H436 (B[0;1m[90mroot      [39m(B[m 20   0 [36m40[39m(B[m492 [36m 9[39m(B[m048 [36m 6[39m(B[m648 S  0.0  0.0  0:00.42 /usr/lib/systemd/(B[0;1m[36msystemd-udevd[17;5H[39m(B[m587 (B[0;1m[90msystemd-t [39m(B[m 20   0 [36m  98M  7[39m(B[m308 [36m 6[39m(B[m364 S  0.0  0.0  0:00.16 /usr/lib/systemd/(B[0;1m[36msystemd-timesyncd[18;5H[39m(B[m601 (B[0;1m[90mdbus      [39m(B[m 20   0 [36m10[39m(B[m980 [36m 4[39m(B[m464 [36m 3[39m(B[m596 S  0.0  0.0  0:01.14 /usr/bin/(B[0;1m[36mdbus-daemon[39m(B[m --system --address=s[19;5H602 (B[0;1m[90mroot      [39m(B[m 20   0 [36m 401M 19[39m(B[m084 [36m16[39m(B[m288 S  0.0  0.1  0:00.74 /usr/bin/(B[0;1m[36mNetworkManager[39m(B[m --no-daemon[20;5H606 (B[0;1m[90mroot      [39m(B[m 20   0 [36m12[39m(B[m580 [36m 5[39m(B[m960 [36m 5[39m(B[m212 S  0.0  0.0  0:00.00 /usr/bin/(B[0;1m[36msshd[39m(B[m -D[21;5H609 (B[0;1m[90mroot      [39m(B[m 20   0 [36m43[39m(B[m028 [36m 3[39m(B[m608 [36m 3[39m(B[m040 S  0.0  0.0  0:00.02 (B[0;1m[36mlogin -- kchibisov[22;5H[39m(B[m611 (B[0;1m[90mroot      [39m(B[m 20   0 [36m26[39m(B[m440 [36m 7[39m(B[m508 [36m 6[39m(B[m536 S  0.0  0.0  0:02.89 /usr/lib/systemd/(B[0;1m[36msystemd-logind[23;5H[39m(B[m629 kchibisov  20   0 [36m32[39m(B[m320 [36m 9[39m(B[m536 [36m 7[39m(B[m788 S  0.0  0.0  0:00.25 /usr/lib/systemd/(B[0;1m[36msystemd[39m(B[m --user[24;5H630 kchibisov  20   0 [36m74[39m(B[m288 [36m 2[39m(B[m792    28 S  0.0  0.0  0:00.00 (B[0;1m[36m(sd-pam)[25;5H[39m(B[m636 kchibisov  20   0 [36m 7[39m(B[m592 [36m 4[39m(B[m128 [36m 3[39m(B[m340 S  0.0  0.0  0:00.00 (B[0;1m[36m-bash[26;5H[39m(B[m678 kchibisov  20   0 [36m 7[39m(B[m124 [36m 2[39m(B[m744 [36m 2[39m(B[m400 S  0.0  0.0  0:00.00 /bin/(B[0;1m[36mbash[39m(B[m /usr/local/bin/start-sway[27;5H679 kchibisov  20   0 [36m1919M  253M  225M [39m(B[mS  0.0  0.8  5:08.53 /home/<USER>/Developer/cc/sway-repaint[28;5H702 kchibisov  20   0 [36m83[39m(B[m092 [36m72[39m(B[m800 [36m39[39m(B[m488 S  0.0  0.2  0:00.12 (B[0;1m[36mswaybg[39m(B[m -o * -i /home/<USER>/Pictures/W[29;5H704 kchibisov  20   0 [36m1200M 49[39m(B[m124 [36m40[39m(B[m400 S  0.0  0.1  0:20.01 (B[0;1m[36mwaybar[39m(B[m -b bar-0
[30dF1[30m[46mHelp  [39;49m(B[mF2[30m[46mSetup [39;49m(B[mF3[30m[46mSearch[39;49m(B[mF4[30m[46mFilter[39;49m(B[mF5[30m[46mTree  [39;49m(B[mF6[30m[46mSortBy[39;49m(B[mF7[30m[46mNice -[39;49m(B[mF8[30m[46mNice +[39;49m(B[mF9[30m[46mKill  [39;49m(B[mF10[30m[46mQuit[K[H[39;49m(B[m[30d[J[?12l[?25h[?1000l[30;1H[?1049l[23;0;0t
[?1l>[?2004h[kchibisov@NightLord forks]$ htop

[?2004l
[?1049h[22;0;0t[1;30r(B[m[4l[?7h[?1h=[?25l[39;49m[?1000h[39;49m(B[m[H[2J[2d  [36m1  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m5  [39m(B[0;1m[[90m             0.0%[39m](B[m   [36m9  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m13 [39m(B[0;1m[[90m             0.0%[39m][3;3H(B[0m[36m2  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m6  [39m(B[0;1m[[90m             0.0%[39m](B[m   [36m10 [39m(B[0;1m[(B[0m[31m|||||||||||100.0%[39m(B[0;1m](B[m    [36m14 [39m(B[0;1m[[90m             0.0%[39m][4;3H(B[0m[36m3  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m7  [39m(B[0;1m[[90m             0.0%[39m](B[m   [36m11 [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m15 [39m(B[0;1m[[90m             0.0%[39m][5;3H(B[0m[36m4  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m8  [39m(B[0;1m[[90m             0.0%[39m](B[m   [36m12 [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m16 [39m(B[0;1m[[90m             0.0%[39m][6;3H(B[0m[36mMem[39m(B[0;1m[(B[0m[32m|||||[34m|[33m|||||||||(B[0;1m[90m[17X[6;39H3.33G/31.3G[39m](B[m   [36mTasks: (B[0;1m[36m66(B[0m[36m; (B[0;1m[32m1(B[0m[36m running[7;3HSwp[39m(B[0;1m[[90m[38X[7;45H0K/0K[39m](B[m   [36mLoad average: [39m(B[0;1m0.67 [36m0.81 (B[0m[36m0.93 [8;54HUptime: (B[0;1m[36m06:04:01
[10d(B[0m[30m[42m    PID USER      PRI  NI  VIRT   RES   SHR S [30m[46mCPU% [30m[42mMEM%   TIME+  Command[K
[11d[30m[46m    679 kchibisov  20   0 1919M  253M  225M S 106.  0.8  5:08.56 /home/<USER>/Developer/cc/sway-repaint[12;2H[39;49m(B[m170469 kchibisov  20   0 [36m 8[39m(B[m664 [36m 4[39m(B[m580 [36m 3[39m(B[m316 [32mR [39m(B[m106.  0.0  0:00.02 (B[0;1m[36mhtop[13;7H[39m(B[m1 (B[0;1m[90mroot      [39m(B[m 20   0 [36m 183M 10[39m(B[m388 [36m 7[39m(B[m868 S  0.0  0.0  0:22.33 /sbin/(B[0;1m[36minit[14;5H[39m(B[m421 (B[0;1m[90mroot      [39m(B[m 20   0 [36m61[39m(B[m176 [36m18[39m(B[m424 [36m17[39m(B[m320 S  0.0  0.1  0:00.60 /usr/lib/systemd/(B[0;1m[36msystemd-journald[15;5H[39m(B[m430 (B[0;1m[90mroot      [39m(B[m 20   0 [36m 148M  1[39m(B[m508 [36m 1[39m(B[m280 S  0.0  0.0  0:00.00 /usr/bin/(B[0;1m[36mlvmetad[39m(B[m -f[16;5H436 (B[0;1m[90mroot      [39m(B[m 20   0 [36m40[39m(B[m492 [36m 9[39m(B[m048 [36m 6[39m(B[m648 S  0.0  0.0  0:00.42 /usr/lib/systemd/(B[0;1m[36msystemd-udevd[17;5H[39m(B[m587 (B[0;1m[90msystemd-t [39m(B[m 20   0 [36m  98M  7[39m(B[m308 [36m 6[39m(B[m364 S  0.0  0.0  0:00.16 /usr/lib/systemd/(B[0;1m[36msystemd-timesyncd[18;5H[39m(B[m601 (B[0;1m[90mdbus      [39m(B[m 20   0 [36m10[39m(B[m980 [36m 4[39m(B[m464 [36m 3[39m(B[m596 S  0.0  0.0  0:01.14 /usr/bin/(B[0;1m[36mdbus-daemon[39m(B[m --system --address=s[19;5H602 (B[0;1m[90mroot      [39m(B[m 20   0 [36m 401M 19[39m(B[m084 [36m16[39m(B[m288 S  0.0  0.1  0:00.74 /usr/bin/(B[0;1m[36mNetworkManager[39m(B[m --no-daemon[20;5H606 (B[0;1m[90mroot      [39m(B[m 20   0 [36m12[39m(B[m580 [36m 5[39m(B[m960 [36m 5[39m(B[m212 S  0.0  0.0  0:00.00 /usr/bin/(B[0;1m[36msshd[39m(B[m -D[21;5H609 (B[0;1m[90mroot      [39m(B[m 20   0 [36m43[39m(B[m028 [36m 3[39m(B[m608 [36m 3[39m(B[m040 S  0.0  0.0  0:00.02 (B[0;1m[36mlogin -- kchibisov[22;5H[39m(B[m611 (B[0;1m[90mroot      [39m(B[m 20   0 [36m26[39m(B[m440 [36m 7[39m(B[m508 [36m 6[39m(B[m536 S  0.0  0.0  0:02.89 /usr/lib/systemd/(B[0;1m[36msystemd-logind[23;5H[39m(B[m629 kchibisov  20   0 [36m32[39m(B[m320 [36m 9[39m(B[m536 [36m 7[39m(B[m788 S  0.0  0.0  0:00.25 /usr/lib/systemd/(B[0;1m[36msystemd[39m(B[m --user[24;5H630 kchibisov  20   0 [36m74[39m(B[m288 [36m 2[39m(B[m792    28 S  0.0  0.0  0:00.00 (B[0;1m[36m(sd-pam)[25;5H[39m(B[m636 kchibisov  20   0 [36m 7[39m(B[m592 [36m 4[39m(B[m128 [36m 3[39m(B[m340 S  0.0  0.0  0:00.00 (B[0;1m[36m-bash[26;5H[39m(B[m678 kchibisov  20   0 [36m 7[39m(B[m124 [36m 2[39m(B[m744 [36m 2[39m(B[m400 S  0.0  0.0  0:00.00 /bin/(B[0;1m[36mbash[39m(B[m /usr/local/bin/start-sway[27;5H702 kchibisov  20   0 [36m83[39m(B[m092 [36m72[39m(B[m800 [36m39[39m(B[m488 S  0.0  0.2  0:00.12 (B[0;1m[36mswaybg[39m(B[m -o * -i /home/<USER>/Pictures/W[28;5H704 kchibisov  20   0 [36m1200M 49[39m(B[m124 [36m40[39m(B[m400 S  0.0  0.1  0:20.01 (B[0;1m[36mwaybar[39m(B[m -b bar-0[29;5H706 kchibisov  20   0 [36m36[39m(B[m324 [36m20[39m(B[m284 [36m17[39m(B[m060 S  0.0  0.1  0:00.42 (B[0;1m[36mmako
[30d[39m(B[mF1[30m[46mHelp  [39;49m(B[mF2[30m[46mSetup [39;49m(B[mF3[30m[46mSearch[39;49m(B[mF4[30m[46mFilter[39;49m(B[mF5[30m[46mTree  [39;49m(B[mF6[30m[46mSortBy[39;49m(B[mF7[30m[46mNice -[39;49m(B[mF8[30m[46mNice +[39;49m(B[mF9[30m[46mKill  [39;49m(B[mF10[30m[46mQuit[K[H[39;49m(B[m[30d[J[?12l[?25h[?1000l[30;1H[?1049l[23;0;0t
[?1l>[?2004h[kchibisov@NightLord forks]$ htop

[?2004l
[?1049h[22;0;0t[1;30r(B[m[4l[?7h[?1h=[?25l[39;49m[?1000h[39;49m(B[m[H[2J[2d  [36m1  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m5  [39m(B[0;1m[[90m             0.0%[39m](B[m   [36m9  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m13 [39m(B[0;1m[[90m             0.0%[39m][3;3H(B[0m[36m2  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m6  [39m(B[0;1m[[90m             0.0%[39m](B[m   [36m10 [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m14 [39m(B[0;1m[(B[0m[32m|||||||||[31m||100.0%[39m(B[0;1m][4;3H(B[0m[36m3  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m7  [39m(B[0;1m[[90m             0.0%[39m](B[m   [36m11 [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m15 [39m(B[0;1m[[90m             0.0%[39m][5;3H(B[0m[36m4  [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m8  [39m(B[0;1m[[90m             0.0%[39m](B[m   [36m12 [39m(B[0;1m[[90m             0.0%[39m](B[m    [36m16 [39m(B[0;1m[[90m             0.0%[39m][6;3H(B[0m[36mMem[39m(B[0;1m[(B[0m[32m|||||[34m|[33m|||||||||(B[0;1m[90m[17X[6;39H3.34G/31.3G[39m](B[m   [36mTasks: (B[0;1m[36m66(B[0m[36m; (B[0;1m[32m1(B[0m[36m running[7;3HSwp[39m(B[0;1m[[90m[38X[7;45H0K/0K[39m](B[m   [36mLoad average: [39m(B[0;1m0.67 [36m0.81 (B[0m[36m0.93 [8;54HUptime: (B[0;1m[36m06:04:02
[10d(B[0m[30m[42m    PID USER      PRI  NI  VIRT   RES   SHR S [30m[46mCPU% [30m[42mMEM%   TIME+  Command[K
[11d[30m[46m 170476 kchibisov  20   0  8664  4676  3416 R 213.  0.0  0:00.03 htop[K[12;7H[39;49m(B[m1 (B[0;1m[90mroot      [39m(B[m 20   0 [36m 183M 10[39m(B[m388 [36m 7[39m(B[m868 S  0.0  0.0  0:22.33 /sbin/(B[0;1m[36minit[13;5H[39m(B[m421 (B[0;1m[90mroot      [39m(B[m 20   0 [36m61[39m(B[m176 [36m18[39m(B[m424 [36m17[39m(B[m320 S  0.0  0.1  0:00.60 /usr/lib/systemd/(B[0;1m[36msystemd-journald[14;5H[39m(B[m430 (B[0;1m[90mroot      [39m(B[m 20   0 [36m 148M  1[39m(B[m508 [36m 1[39m(B[m280 S  0.0  0.0  0:00.00 /usr/bin/(B[0;1m[36mlvmetad[39m(B[m -f[15;5H436 (B[0;1m[90mroot      [39m(B[m 20   0 [36m40[39m(B[m492 [36m 9[39m(B[m048 [36m 6[39m(B[m648 S  0.0  0.0  0:00.42 /usr/lib/systemd/(B[0;1m[36msystemd-udevd[16;5H[39m(B[m587 (B[0;1m[90msystemd-t [39m(B[m 20   0 [36m  98M  7[39m(B[m308 [36m 6[39m(B[m364 S  0.0  0.0  0:00.16 /usr/lib/systemd/(B[0;1m[36msystemd-timesyncd[17;5H[39m(B[m601 (B[0;1m[90mdbus      [39m(B[m 20   0 [36m10[39m(B[m980 [36m 4[39m(B[m464 [36m 3[39m(B[m596 S  0.0  0.0  0:01.14 /usr/bin/(B[0;1m[36mdbus-daemon[39m(B[m --system --address=s[18;5H602 (B[0;1m[90mroot      [39m(B[m 20   0 [36m 401M 19[39m(B[m084 [36m16[39m(B[m288 S  0.0  0.1  0:00.74 /usr/bin/(B[0;1m[36mNetworkManager[39m(B[m --no-daemon[19;5H606 (B[0;1m[90mroot      [39m(B[m 20   0 [36m12[39m(B[m580 [36m 5[39m(B[m960 [36m 5[39m(B[m212 S  0.0  0.0  0:00.00 /usr/bin/(B[0;1m[36msshd[39m(B[m -D[20;5H609 (B[0;1m[90mroot      [39m(B[m 20   0 [36m43[39m(B[m028 [36m 3[39m(B[m608 [36m 3[39m(B[m040 S  0.0  0.0  0:00.02 (B[0;1m[36mlogin -- kchibisov[21;5H[39m(B[m611 (B[0;1m[90mroot      [39m(B[m 20   0 [36m26[39m(B[m440 [36m 7[39m(B[m508 [36m 6[39m(B[m536 S  0.0  0.0  0:02.89 /usr/lib/systemd/(B[0;1m[36msystemd-logind[22;5H[39m(B[m629 kchibisov  20   0 [36m32[39m(B[m320 [36m 9[39m(B[m536 [36m 7[39m(B[m788 S  0.0  0.0  0:00.25 /usr/lib/systemd/(B[0;1m[36msystemd[39m(B[m --user[23;5H630 kchibisov  20   0 [36m74[39m(B[m288 [36m 2[39m(B[m792    28 S  0.0  0.0  0:00.00 (B[0;1m[36m(sd-pam)[24;5H[39m(B[m636 kchibisov  20   0 [36m 7[39m(B[m592 [36m 4[39m(B[m128 [36m 3[39m(B[m340 S  0.0  0.0  0:00.00 (B[0;1m[36m-bash[25;5H[39m(B[m678 kchibisov  20   0 [36m 7[39m(B[m124 [36m 2[39m(B[m744 [36m 2[39m(B[m400 S  0.0  0.0  0:00.00 /bin/(B[0;1m[36mbash[39m(B[m /usr/local/bin/start-sway[26;5H679 kchibisov  20   0 [36m1919M  253M  225M [39m(B[mS  0.0  0.8  5:08.58 /home/<USER>/Developer/cc/sway-repaint[27;5H702 kchibisov  20   0 [36m83[39m(B[m092 [36m72[39m(B[m800 [36m39[39m(B[m488 S  0.0  0.2  0:00.12 (B[0;1m[36mswaybg[39m(B[m -o * -i /home/<USER>/Pictures/W[28;5H704 kchibisov  20   0 [36m1200M 49[39m(B[m124 [36m40[39m(B[m400 S  0.0  0.1  0:20.01 (B[0;1m[36mwaybar[39m(B[m -b bar-0[29;5H706 kchibisov  20   0 [36m36[39m(B[m324 [36m20[39m(B[m284 [36m17[39m(B[m060 S  0.0  0.1  0:00.42 (B[0;1m[36mmako
[30d[39m(B[mF1[30m[46mHelp  [39;49m(B[mF2[30m[46mSetup [39;49m(B[mF3[30m[46mSearch[39;49m(B[mF4[30m[46mFilter[39;49m(B[mF5[30m[46mTree  [39;49m(B[mF6[30m[46mSortBy[39;49m(B[mF7[30m[46mNice -[39;49m(B[mF8[30m[46mNice +[39;49m(B[mF9[30m[46mKill  [39;49m(B[mF10[30m[46mQuit[K[H[39;49m(B[m[30d[J[?12l[?25h[?1000l[30;1H[?1049l[23;0;0t
[?1l>[?2004h[kchibisov@NightLord forks]$ htop

[?2004l
[?1049h[22;0;0t[1;30r(B[m[4l[?7h[?1h=[?25l[39;49m[?1000h[?2004h[kchibisov@NightLord alacritty]$ [?2004l

exit
