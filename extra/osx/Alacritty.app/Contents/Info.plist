<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>CFBundleDevelopmentRegion</key>
  <string>en</string>
  <key>CFBundleExecutable</key>
  <string>alacritty</string>
  <key>CFBundleIdentifier</key>
  <string>org.alacritty</string>
  <key>CFBundleInfoDictionaryVersion</key>
  <string>6.0</string>
  <key>CFBundleName</key>
  <string>Alacritty</string>
  <key>CFBundlePackageType</key>
  <string>APPL</string>
  <key>CFBundleShortVersionString</key>
  <string>0.17.0-dev</string>
  <key>CFBundleSupportedPlatforms</key>
  <array>
    <string>MacOSX</string>
  </array>
  <key>CFBundleVersion</key>
  <string>1</string>
  <key>CFBundleIconFile</key>
  <string>alacritty.icns</string>
  <key>NSHighResolutionCapable</key>
  <true/>
  <key>NSMainNibFile</key>
  <string></string>
  <key>NSSupportsAutomaticGraphicsSwitching</key>
  <true/>
  <key>CFBundleDisplayName</key>
  <string>Alacritty</string>
  <key>NSRequiresAquaSystemAppearance</key>
  <string>NO</string>
    <key>NSAppleEventsUsageDescription</key>
    <string>An application in Alacritty would like to access AppleScript.</string>
    <key>NSCalendarsUsageDescription</key>
    <string>An application in Alacritty would like to access calendar data.</string>
    <key>NSCameraUsageDescription</key>
    <string>An application in Alacritty would like to access the camera.</string>
    <key>NSContactsUsageDescription</key>
    <string>An application in Alacritty wants to access your contacts.</string>
    <key>NSLocationAlwaysUsageDescription</key>
    <string>An application in Alacritty would like to access your location information, even in the background.</string>
    <key>NSLocationUsageDescription</key>
    <string>An application in Alacritty would like to access your location information.</string>
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>An application in Alacritty would like to access your location information while active.</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>An application in Alacritty would like to access your microphone.</string>
    <key>NSRemindersUsageDescription</key>
    <string>An application in Alacritty would like to access your reminders.</string>
    <key>NSSystemAdministrationUsageDescription</key>
    <string>An application in Alacritty requires elevated permissions.</string>
    <key>NSBluetoothAlwaysUsageDescription</key>
    <string>An application in Alacritty wants to use Bluetooth.</string>
</dict>
</plist>
