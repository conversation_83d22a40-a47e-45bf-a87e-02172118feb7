image: freebsd/latest

packages:
  - devel/cmake
  - devel/pkgconf
  - lang/python3
  - print/freetype2
  - x11-fonts/fontconfig
  - x11-fonts/dejavu
  - x11/libxcb
  - x11/libxkbcommon

sources:
  - https://github.com/alacritty/alacritty

environment:
  PATH: /home/<USER>/.cargo/bin:/bin:/usr/bin:/usr/local/bin

tasks:
  - rustup: |
      curl https://sh.rustup.rs -sSf | sh -s -- -y --default-toolchain stable --profile minimal
  - test: |
      cd alacritty
      cargo test
  - oldstable: |
      cd alacritty
      oldstable=$(cat Cargo.toml | grep "rust-version" | sed 's/.*"\(.*\)".*/\1/')
      rustup toolchain install --profile minimal $oldstable
      rustup default $oldstable
      cargo test
  - clippy: |
      cd alacritty
      rustup component add clippy
      cargo clippy --all-targets
  - feature-wayland: |
      cd alacritty/alacritty
      RUSTFLAGS="-D warnings" cargo test --no-default-features --features=wayland
  - feature-x11: |
      cd alacritty/alacritty
      RUSTFLAGS="-D warnings" cargo test --no-default-features --features=x11
