[package]
name = "alacritty_config"
version = "0.2.4-dev"
authors = ["<PERSON> <<EMAIL>>"]
license = "MIT OR Apache-2.0"
description = "Alacritty configuration abstractions"
homepage = "https://alacritty.org"
repository = "https://github.com/alacritty/alacritty"
edition.workspace = true
rust-version.workspace = true

[dependencies]
log = { version = "0.4.17", features = ["serde"] }
serde = "1.0.163"
toml.workspace = true

[dev-dependencies]
alacritty_config_derive = { version = "0.2.6-dev", path = "../alacritty_config_derive" }
serde = { version = "1.0.163", features = ["derive"] }
