use std::time::Duration;

use serde::Serialize;

use alacritty_config_derive::ConfigDeserialize;

use crate::config::ui_config::Program;
use crate::display::color::Rgb;

#[derive(ConfigDeserialize, Ser<PERSON><PERSON>, <PERSON><PERSON>, Debug, PartialEq, Eq)]
pub struct BellConfig {
    /// Visual bell animation function.
    pub animation: BellAnimation,

    /// Command to run on bell.
    pub command: Option<Program>,

    /// Visual bell flash color.
    pub color: Rgb,

    /// Visual bell duration in milliseconds.
    duration: u16,
}

impl Default for BellConfig {
    fn default() -> Self {
        Self {
            color: Rgb::new(255, 255, 255),
            animation: Default::default(),
            command: Default::default(),
            duration: Default::default(),
        }
    }
}

impl BellConfig {
    pub fn duration(&self) -> Duration {
        Duration::from_millis(self.duration as u64)
    }
}

/// `VisualBellAnimations` are modeled after a subset of CSS transitions and Robert
/// <PERSON><PERSON>'s Easing Functions.
#[derive(ConfigDeserialize, <PERSON><PERSON><PERSON>, De<PERSON>ult, <PERSON><PERSON>, <PERSON><PERSON>, Debug, <PERSON>ialEq, Eq)]
pub enum BellAnimation {
    // CSS animation.
    Ease,
    // CSS animation.
    EaseOut,
    // Penner animation.
    EaseOutSine,
    // Penner animation.
    EaseOutQuad,
    // Penner animation.
    EaseOutCubic,
    // Penner animation.
    EaseOutQuart,
    // Penner animation.
    EaseOutQuint,
    // Penner animation.
    EaseOutExpo,
    // Penner animation.
    EaseOutCirc,
    // Penner animation.
    #[default]
    Linear,
}
